<?php

namespace App\Services\Admin\AwarenessManagement;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class TopicValidator
{
    protected $rules = [
        'title' => 'required|string',
    ];

    protected $updateRules = [
        'title' => 'sometimes|required|string',
        'order' => 'sometimes|integer',
    ];

    public function validate(Request $request, bool $isCreate = true): array
    {
        $rules = $isCreate ? $this->rules : $this->updateRules;
        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return $request->only(array_keys($rules));
    }
}