<?php

namespace App\Http\Controllers;

use App\Helpers;
use App\Models\User;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Routing\Redirector;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Tymon\JWTAuth\Facades\JWTAuth;
use Tymon\JWTAuth\Exceptions\JWTException;
use Illuminate\Support\Facades\Log;
use App\Services\SubscriptionService;
use App\Services\NotificationService;
use App\Models\Evaluation;



class AuthController extends BaseController
{

    /**
     * Register a new user via API.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function Register(Request $request)
    {
        try {
            $input = $request->input();

            $validator = Validator::make($input, [
                'first_name' => 'required|min:3',
                'last_name' => 'required|min:3',
                'email' => 'required|unique:users,email',
                'password' => 'required|confirmed|min:6',
                'g-recaptcha-response' => 'required',
            ]);

            if ($validator->fails()) {
                return response()->json(['status' => 500, 'error' => $validator->errors()]);
            }

            $recaptcha = Helpers::verifyRecaptcha($input['g-recaptcha-response']);
            if (!$recaptcha['success']) {
                return response()->json(['status' => 422, 'error' => ['recaptcha' => ['Recaptcha failed']]], 422);
            }
                
            $user = User::create([
                'first_name' => $input['first_name'],
                'last_name' => $input['last_name'],
                'name' => $input['first_name'] . ' ' . $input['last_name'],
                'email' => $input['email'],
                'password' => bcrypt($input['password']),
                'phone' => $input['phone'] ?? null,
                'company' => $input['company'] ?? null,
                'website' => $input['website'] ?? null,
                'stripe_customer' => null,
                'subscription' => null,
                'subscription_type' => 'free',
                'activation_code' => null,
                'reset_code' => null,
                'remember_token' => null,
            ]);

            $token = JWTAuth::fromUser($user);
            $subscriptionService = new SubscriptionService();
            $package = $subscriptionService->createFreeSubscription($user->_id);

            // Send welcome notification to new user
            NotificationService::welcomeUser($user->_id, $user->name);

            // Send getting started tips
            NotificationService::create(
                $user->_id,
                'Getting Started with RaiDOT',
                'Start your AI risk evaluation journey! Try our risk assessment tools and explore our awareness courses.',
                'getting_started',
                '/portal/dashboard',
                ['step' => 'registration_complete']
            );

            return response()->json([
                'status' => 200, 
                'msg' => 'Registration successful', 
                'token' => $token, 
                'user' => $user, 
                'pkg' => $package
            ]);
            
        } catch (\Exception $e) {
            return response()->json(['status' => 500, 'error' => $e->getMessage()]);
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     *
     * User login API
     */
    public function Login(Request $request): JsonResponse
    {
        try {
            $input = $request->input();
            $validator = Validator::make($input, [
                'email' => 'required|exists:users,email',
                'password' => 'required|min:6',
                'g-recaptcha-response' => 'required',
            ]);
    
            if ($validator->fails()) {
                return response()->json(['status' => 500, 'error' => $validator->errors()]);
            }
    
            $recaptcha = Helpers::verifyRecaptcha($input['g-recaptcha-response']);
            if (!$recaptcha['success']) {
                return response()->json(['status' => 422, 'error' => ['recaptcha' => ['Recaptcha failed']]], 422);
            }
    
            if (!$token = JWTAuth::attempt($request->only('email', 'password'))) {
                // Send security alert for failed login attempt
                $user = User::where('email', $input['email'])->first();
                if ($user) {
                    NotificationService::securityAlert(
                        $user->_id,
                        'Failed login attempt detected for your account. If this wasn\'t you, please secure your account.'
                    );
                }
                return response()->json(['status' => 401,'error' => ['email' => ['Invalid Credential! Please try again.']]]);
            }
    
            $user = Auth::user();
            Log::info('User logged in successfully', ['user' => $user]);
    
            if (!isset($user->subscription_type) || empty($user->subscription_type)) {
                $user->stripe_customer = null;
                $user->subscription = null;
                $user->subscription_type = 'free';
                $user->save();
            } elseif ($user->subscription != null) {
                $user->subscription_type = 'premium';
                $user->save();
            }

            // Send login notification
            NotificationService::create(
                $user->_id,
                'Welcome Back!',
                'You have successfully logged into your RaiDOT account.',
                'login_success',
                '/portal/dashboard',
                ['login_time' => now()->toDateTimeString()]
            );

            // Check if user has unfinished evaluations and remind them
            $unfinishedEvaluations = Evaluation::where('user_id', $user->_id)
                ->whereNull('report')
                ->count();

            if ($unfinishedEvaluations > 0) {
                NotificationService::create(
                    $user->_id,
                    'Continue Your Evaluations',
                    "You have {$unfinishedEvaluations} unfinished evaluation(s). Complete them to get your risk analysis results.",
                    'reminder',
                    '/portal/user/evaluation/all',
                    ['count' => $unfinishedEvaluations]
                );
            }

            return response()->json(['status' => 200, 'msg' => 'Login successful', 'token' => $token, 'user' => $user]);
        } catch (JWTException $e) {
            return response()->json(['status' => 500, 'error' => ['server' => ['$e->getMessage()']]]);
        }
    }

    /**
     * Log out the currently authenticated user.
     *
     * @param Request $request
     * @return RedirectResponse
     */
    public function Logout()
    {
        try {
            JWTAuth::invalidate(JWTAuth::getToken());
            Log::info('User logged out successfully');
            return response()->json(['status' => 200, 'msg' => 'Successfully logged out']);
        } catch (JWTException $e) {
            Log::info('Exception occured', ['error' => $e->getMessage()]);
            return response()->json(['status' => 500, 'msg' => 'Could not log out'], 500);
        }
    }

    public function Refresh()
    {
        Log::info('Refresh token request');
        try {
            $newToken = JWTAuth::parseToken()->refresh();
            Log::info('Token Generated: ', ['token' => $newToken]);
            return response()->json([ 'status' => 200, 'token' => $newToken ]);
        } catch (JWTException $e) {
            Log::info('Error: ', ['error' => $e->getMessage()]);
            return response()->json([ 'status' => 401, 'error' => 'Token cannot be refreshed, please login again' ], 401);
        }
    }



    public function checkTokenStatus(Request $request)
    {
        try {
            // Get the token from the request
            $token = JWTAuth::getToken();

            if (!$token) {
                return response()->json(['status' => 'Token not provided'], 400);
            }
            $user = JWTAuth::authenticate($token);
            return response()->json([
                'status' => 'Token is valid',
                'user' => $user,
                'token' => $token
            ]);
        } catch (\Tymon\JWTAuth\Exceptions\TokenExpiredException $e) {
            return response()->json(['status' => 'Token expired'], 401);
        } catch (\Tymon\JWTAuth\Exceptions\TokenInvalidException $e) {
            return response()->json(['status' => 'Token invalid'], 401);
        } catch (JWTException $e) {
            return response()->json(['status' => 'Token error', 'message' => $e->getMessage()], 500);
        }
    }


    /**
     * @param Request $request
     * @return RedirectResponse
     *
     * Process the forgot password request.
     *
     *  Validates the provided email, generates a password reset link, sends it via email,
     *  and redirects the user with success or error messages accordingly.
     */
    public function ForgotPassword(Request $request)
    {
        try {
            $input = $request->input();
            $validator = Validator::make($input, [
                'email' => 'required|exists:users,email',
                'resetUrl' => 'required|url'
            ]);
            if ($validator->fails()) {
                return response()->json(['status' => 422,'error' => $validator->errors()], 422);
            }

            $user = User::where('email', $input['email'])->first();
            if ($user != null) {
                $reset_code = time() . $user->id;
                $user->reset_code = $reset_code;
                $user->save();
                
                $resetLink = rtrim($input['resetUrl'], '/') . '/' . $reset_code;
                Log::info('Password reset link generated:', ['link' => $resetLink]);

                Mail::send('emails.forgot', ['userInfo' => $user, 'reset_link' => $resetLink], function ($message) use ($user) {
                    $message->to($user->email, $user->name)->subject(env('MAIL_FROM_NAME') . ': Password Reset Link');
                    $message->from(env('MAIL_FROM_ADDRESS'), env('MAIL_FROM_NAME'));
                });

                // Send password reset notification
                NotificationService::create(
                    $user->_id,
                    'Password Reset Requested',
                    'A password reset link has been sent to your email address. The link will expire in 24 hours.',
                    'password_reset',
                    null,
                    ['reset_time' => now()->toDateTimeString()]
                );

                $loggedEmailBody = view('emails.forgot', ['userInfo' => $user,'reset_link' => $resetLink])->render();
                Log::info('Password reset email sent with body:', [ 'to' => $user->email, 'subject' => env('MAIL_FROM_NAME') . ': Password Reset Link', 'body' => $loggedEmailBody ]);
                return response()->json(['status' => 200,'msg' => 'A password reset link has been sent to your email address. Please check your mail inbox.']);
            } else {
                return response()->json(['status' => 404,'error' => ['email' => ['User not found! Please check your email address.']]], 404);
            }
        } catch (\Exception $e) {
            return response()->json(['status' => 500,'error' => $e->getMessage()], 500);
        }
    }


    /**
     * Reset the user's password using the provided reset code.
     *
     * @param Request $request
     * @param string $reset_code
     * @return Application|RedirectResponse|Redirector
     */
    public function ResetPassword(Request $request)
    {
        try {
            $input = $request->input();

            // Validate input data
            $validator = Validator::make($input, [
                'password' => 'required|confirmed|min:6',
                'reset_code' => 'required'
            ]);

            if ($validator->fails()) {
                return response()->json(['status' => 422,'error' => $validator->errors()], 422);
            }

            $user = User::where('reset_code', $input['reset_code'])->first();
            Log::info('Reset password request:', ['user' => $user, 'reset_code' => $input['reset_code']]);
            
            if ($user != null) {
                $user->reset_code = null;
                $user->password = bcrypt($input['password']);
                $user->save();

                // Send password reset success notification
                NotificationService::create(
                    $user->_id,
                    'Password Reset Successful',
                    'Your password has been successfully reset. If you didn\'t make this change, please contact support immediately.',
                    'password_reset_success',
                    '/portal/profile',
                    ['reset_time' => now()->toDateTimeString()]
                );

                // Send security alert
                NotificationService::securityAlert(
                    $user->_id,
                    'Your account password was recently changed. If this wasn\'t you, please contact support immediately.'
                );

                return response()->json(['status' => 200,'msg' => 'Your password has been reset successfully. Please log in.']);
            } else {
                return response()->json(['status' => 404,'error' => ['reset_code' => ['Invalid or expired reset code.']]], 404);
            }
        } catch (\Exception $e) {
            return response()->json(['status' => 500,'error' => $e->getMessage()], 500);
        }
    }
}
