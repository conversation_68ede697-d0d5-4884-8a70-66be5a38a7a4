<?php

namespace App\Models;

use <PERSON><PERSON><PERSON>\Mongodb\Eloquent\Model as Eloquent;

class AwarenessTopicLessonRead extends Eloquent
{
    /**
     * The connection name for the model.
     *
     * @var string
     */
    protected $connection = 'mongodb';

    /**
     * The collection associated with the model.
     *
     * @var string
     */
    protected $collection = 'awareness_topic_lesson_read';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $fillable = [
        'awareness_id',
        'topic_id',
        'lesson_id',
        'user_id',
        'is_guest',
        'is_quiz',
        'is_final_exam',
        'completed',
        'score',
        'submissions',
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'updated_at',
        'created_at'
    ];

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;
}
