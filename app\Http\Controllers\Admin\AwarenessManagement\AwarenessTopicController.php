<?php

namespace App\Http\Controllers\Admin\AwarenessManagement;

use App\Http\Controllers\Controller;
use App\Interfaces\Admin\AwarenessManagement\AwarenessTopicRepositoryInterface;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class AwarenessTopicController extends Controller
{
    private $awarenessTopicRepository;

    public function __construct(AwarenessTopicRepositoryInterface $awarenessTopicRepository)
    {
        $this->awarenessTopicRepository = $awarenessTopicRepository;
    }

    public function list(Request $request, string $awarenessId): JsonResponse
    {
        $response = $this->awarenessTopicRepository->list($request, $awarenessId);
        return response()->json($response, $response['status']);
    }

    public function create(Request $request, string $awarenessId): JsonResponse
    {
        $response = $this->awarenessTopicRepository->create($request, $awarenessId);
        return response()->json($response, $response['status']);
    }

    public function update(Request $request, string $awarenessId, string $topicId): JsonResponse
    {
        $response = $this->awarenessTopicRepository->update($request, $awarenessId, $topicId);
        return response()->json($response, $response['status']);
    }

    public function delete(Request $request, string $awarenessId, string $topicId): JsonResponse
    {
        $response = $this->awarenessTopicRepository->delete($request, $awarenessId, $topicId);
        return response()->json($response, $response['status']);
    }
}
