<?php

namespace App\Services\Admin\AwarenessManagement;

use Illuminate\Support\Facades\Storage;

class ImageHandler
{
    public function handleBase64Images(?string $html): ?string
    {
        if (empty($html)) {
            return null;
        }

        $htmlDom = new \DOMDocument();
        @$htmlDom->loadHTML($html);
        $imageTags = $htmlDom->getElementsByTagName('img');

        foreach ($imageTags as $imageTag) {
            $imgSrc = $imageTag->getAttribute('src');
            if (strpos($imgSrc, ';base64') !== false) {
                $imgData = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $imgSrc));
                $fileName = time() . '-' . uniqid() . '.png';
                $filePath = 'editor/image/' . $fileName;
                Storage::disk('public')->put($filePath, $imgData);
                $fileUrl = asset('storage/' . $filePath);
                $html = str_replace($imgSrc, $fileUrl, $html);
            }
        }

        return $html;
    }
}