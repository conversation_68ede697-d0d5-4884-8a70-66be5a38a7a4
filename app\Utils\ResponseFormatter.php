<?php

namespace App\Utils;

/**
 * Class ResponseFormatter
 *
 * Utility class for formatting JSON responses.
 *
 * @package App\Utils
 */
class ResponseFormatter
{
    /**
     * Format a successful JSON response.
     *
     * @param string $message
     * @param mixed $data
     * @param int $statusCode
     * @return \Illuminate\Http\JsonResponse
     */
    public static function success(string $message, $data = null, int $statusCode = 200)
    {
        return response()->json([
            'status' => $statusCode,
            'msg' => $message,
            'data' => $data,
        ], $statusCode);
    }

    /**
     * Format an error JSON response.
     *
     * @param string $message
     * @param int $statusCode
     * @return \Illuminate\Http\JsonResponse
     */
    public static function error(string $message, int $statusCode = 400)
    {
        return response()->json([
            'status' => $statusCode,
            'msg' => $message,
        ], $statusCode);
    }
}