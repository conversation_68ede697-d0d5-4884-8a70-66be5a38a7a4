<?php

namespace App\Models;

use <PERSON><PERSON><PERSON>\Mongodb\Eloquent\Model as Eloquent;

class Awareness extends Eloquent
{
    protected $connection = 'mongodb';
    protected $collection = 'awareness';
    protected $fillable = [
        'title',
        'slug',
        'description',
        'banner',
        'package_id',
        'package_name',
        'issue_certificate'
    ];
    protected $hidden = [
        'updated_at',
        'created_at'
    ];
    public $timestamps = true;

    protected $appends = ['banner_full_path'];

    public function getBannerFullPathAttribute()
    {
        if(isset($this->attributes['banner']) && $this->attributes['banner'] != null) {
            return asset('/storage/media/image/' . $this->attributes['banner']);
        }
        return null;
    }

    public function topics()
    {
        return $this->hasMany(AwarenessTopic::class, 'awareness_id');
    }
    public function package()
    {
        return $this->belongsTo(Pricing::class, 'package_id', '_id');
    }

    public static function getWithAllDataExceptAnswer($id)
    {
        return self::with([
            'package',
            'topics' => function($query) {$query->orderBy('order');},
            'topics.lessons' => function($query) {$query->orderBy('order');},
            'topics.lessons.questions' => function($query) {$query->orderBy('order')->select(['_id', 'lesson_id', 'order', 'question', 'option_1', 'option_2', 'option_3', 'option_4']);
            }
        ])->find($id);
    }

    public static function getAllWithAllDataExceptAnswer()
    {
        return self::with([
            'package',
            'topics' => function($query) {$query->orderBy('order');},
            'topics.lessons' => function($query) {$query->orderBy('order');},
            'topics.lessons.questions' => function($query) {$query->orderBy('order')->select(['_id', 'lesson_id', 'order', 'question', 'option_1', 'option_2', 'option_3', 'option_4']);}
        ])->get();
    }
}