<?php
// FIXED app/Models/ConsultancySlot.php

namespace App\Models;

use <PERSON><PERSON><PERSON>\Mongodb\Eloquent\Model;
use Carbon\Carbon;

class ConsultancySlot extends Model
{
    protected $connection = 'mongodb';
    protected $collection = 'consultancy_slots';

    protected $fillable = [
        'date',
        'start_time',
        'end_time',
        'duration_minutes',
        'type', // 'online' or 'in_person'
        'location', // for in-person slots
        'country',
        'city',
        'address',
        'max_capacity',
        'is_active',
        'created_by_admin',
        'notes',
        'meeting_link_template', // for online sessions
    ];

    // REMOVED PROBLEMATIC CASTS - Let MongoDB handle the date storage
    protected $casts = [
        'duration_minutes' => 'integer',
        'max_capacity' => 'integer',
        'is_active' => 'boolean',
    ];

    public function bookings()
    {
        return $this->hasMany(ConsultancyBooking::class, 'slot_id');
    }

    public function getAvailableCapacityAttribute()
    {
        return $this->max_capacity - $this->bookings()->where('status', 'confirmed')->count();
    }

    public function getIsFullAttribute()
    {
        return $this->available_capacity <= 0;
    }

    // FIXED: Handle date conversion manually to avoid casting issues
    public function getFormattedTimeAttribute()
    {
        try {
            $start = is_array($this->start_time) ? 
                Carbon::parse($this->start_time['date'] ?? $this->start_time) : 
                Carbon::parse($this->start_time);
            
            $end = is_array($this->end_time) ? 
                Carbon::parse($this->end_time['date'] ?? $this->end_time) : 
                Carbon::parse($this->end_time);
            
            return $start->format('H:i') . ' - ' . $end->format('H:i');
        } catch (\Exception $e) {
            return 'Invalid time';
        }
    }

    public function getFormattedDateAttribute()
    {
        try {
            $date = is_array($this->date) ? 
                Carbon::parse($this->date['date'] ?? $this->date) : 
                Carbon::parse($this->date);
            
            return $date->format('F j, Y');
        } catch (\Exception $e) {
            return 'Invalid date';
        }
    }

    public function getDurationHoursAttribute()
    {
        return round($this->duration_minutes / 60, 2);
    }

    public function getIsPastAttribute()
    {
        try {
            $slotDateTime = is_array($this->date) ? 
                Carbon::parse($this->date['date'] ?? $this->date) : 
                Carbon::parse($this->date);
            
            $startTime = is_array($this->start_time) ? 
                Carbon::parse($this->start_time['date'] ?? $this->start_time) : 
                Carbon::parse($this->start_time);
            
            return $slotDateTime->copy()->setTimeFrom($startTime)->isPast();
        } catch (\Exception $e) {
            return false;
        }
    }

    // FIXED: Override boot method to handle date conversion properly
    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($slot) {
            $slot->processDateTimeFields();
        });
        
        static::updating(function ($slot) {
            $slot->processDateTimeFields();
        });
    }

    /**
     * Process date/time fields to ensure consistent storage
     */
    private function processDateTimeFields()
    {
        try {
            // Handle date field
            if ($this->date && !is_array($this->date)) {
                $this->date = Carbon::parse($this->date)->toDateString();
            }

            // Handle start_time
            if ($this->start_time && !is_array($this->start_time)) {
                if ($this->date) {
                    // If we have both date and time, combine them
                    $dateStr = is_array($this->date) ? $this->date['date'] : $this->date;
                    $this->start_time = Carbon::parse($dateStr . ' ' . $this->start_time)->toDateTimeString();
                } else {
                    $this->start_time = Carbon::parse($this->start_time)->toDateTimeString();
                }
            }

            // Handle end_time
            if ($this->end_time && !is_array($this->end_time)) {
                if ($this->date) {
                    // If we have both date and time, combine them
                    $dateStr = is_array($this->date) ? $this->date['date'] : $this->date;
                    $this->end_time = Carbon::parse($dateStr . ' ' . $this->end_time)->toDateTimeString();
                } else {
                    $this->end_time = Carbon::parse($this->end_time)->toDateTimeString();
                }
            }

            // Calculate duration if both times are set
            if ($this->start_time && $this->end_time && !$this->duration_minutes) {
                $start = Carbon::parse($this->start_time);
                $end = Carbon::parse($this->end_time);
                $this->duration_minutes = $end->diffInMinutes($start);
            }
        } catch (\Exception $e) {
            // Log error but don't break the save process
            \Log::warning('Date processing error in ConsultancySlot: ' . $e->getMessage());
        }
    }

    /**
     * Get start time as Carbon instance safely
     */
    public function getStartTimeAttribute($value)
    {
        if (is_array($value)) {
            return $value['date'] ?? null;
        }
        return $value;
    }

    /**
     * Get end time as Carbon instance safely
     */
    public function getEndTimeAttribute($value)
    {
        if (is_array($value)) {
            return $value['date'] ?? null;
        }
        return $value;
    }

    /**
     * Get date as string safely
     */
    public function getDateAttribute($value)
    {
        if (is_array($value)) {
            return $value['date'] ?? null;
        }
        return $value;
    }
}