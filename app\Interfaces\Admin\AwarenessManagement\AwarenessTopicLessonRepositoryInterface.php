<?php

namespace App\Interfaces\Admin\AwarenessManagement;

use Illuminate\Http\Request;

interface AwarenessTopicLessonRepositoryInterface
{
    public function create(Request $request, string $awarenessId, string $topicId): array;
    public function single(Request $request, string $awarenessId, string $topicId, string $lessonId): array;
    public function update(Request $request, string $awarenessId, string $topicId, string $lessonId): array;
    public function delete(Request $request, string $awarenessId, string $topicId, string $lessonId): array;
}