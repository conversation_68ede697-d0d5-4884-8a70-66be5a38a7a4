<?php

namespace App\Models;

use <PERSON><PERSON><PERSON>\Mongodb\Eloquent\Model as Eloquent;

class AwarenessTopicLessonQuestion extends Eloquent
{
    protected $connection = 'mongodb';
    protected $collection = 'awareness_topic_lesson_questions';
    protected $fillable = [
        'lesson_id',
        'order',
        'question',
        'option_1',
        'option_2',
        'option_3',
        'option_4',
        'answer'
    ];
    protected $hidden = [
        'updated_at',
        'created_at'
    ];
    public $timestamps = true;

    public function lesson()
    {
        return $this->belongsTo(AwarenessTopicLesson::class, 'lesson_id');
    }
}