<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\PortalController;
use App\Http\Controllers\EvaluationController;
use App\Http\Controllers\AwarenessController;
use App\Http\Controllers\SubscriptionController;
use App\Http\Controllers\SearchController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\Admin\PricingController;
use App\Http\Controllers\User\ConsultancyController as UserConsultancyController;

Route::get('/user/token-refresh', [AuthController::class, 'Refresh'])->name('api.user.token.refresh');
Route::get('/debug-token', [AuthController::class, 'checkTokenStatus']);

Route::middleware('UserAuthCheck')->group(function () {
    Route::post('/user-register', [AuthController::class, 'Register'])->name('api.user.register');
    Route::post('/user-login', [AuthController::class, 'Login'])->name('api.user.login');
    Route::post('/user-forgot-password', [AuthController::class, 'ForgotPassword'])->name('api.user.forgot.password');
    Route::post('/user-reset-password', [AuthController::class, 'ResetPassword'])->name('api.user.reset.password');
});

Route::middleware('auth:api')->group(function () {
    Route::get('/user-logout', [AuthController::class, 'Logout'])->name('api.user.logout');
    
    // Subscription routes
    Route::post('/subscribe/checkout', [SubscriptionController::class, 'createCheckoutSession']);
    Route::post('/subscribe/success', [SubscriptionController::class, 'handleCheckoutSuccess']);
    Route::get('/subscribe/cancel', [SubscriptionController::class, 'cancelSubscription']);
    Route::get('/subscription', [SubscriptionController::class, 'getSubscription']);
    Route::get('/pricing/list', [PricingController::class, 'getAllPricing']);
    
    // Search routes
    Route::post('/search', [SearchController::class, 'search']);
    Route::get('/search/suggestions', [SearchController::class, 'suggestions']);
    
    // Notification routes
    Route::prefix('notifications')->group(function () {
        Route::get('/', [NotificationController::class, 'index'])->name('api.notifications');
        Route::get('/count', [NotificationController::class, 'count'])->name('api.notifications.count');
        Route::post('/mark-read', [NotificationController::class, 'markAsRead'])->name('api.notifications.mark-read');
        Route::post('/mark-all-read', [NotificationController::class, 'markAllAsRead'])->name('api.notifications.mark-all-read');
        Route::delete('/{id}', [NotificationController::class, 'delete'])->name('api.notifications.delete');
    });
    
    Route::prefix('/portal')->group(function () {    
        //API routes for user profile related actions
        Route::group(['prefix' => 'profile'], function () {
            Route::post('/update', [PortalController::class, 'profile_update'])->name('api.user.profile.update');
            Route::post('/update-password', [PortalController::class, 'profile_update_password'])->name('api.user.profile.update.password');
        });

        // API routes for user-evaluation related actions
        Route::group(['prefix' => 'user'], function () {
            Route::get('/evaluation/all', [PortalController::class, 'user_evaluations'])->name('api.user.evaluations');
            Route::post('/evaluation/delete', [EvaluationController::class, 'user_evaluation_delete'])->name('api.user.evaluation.delete');
            Route::post('/evaluation/report', [EvaluationController::class, 'user_evaluation_report'])->name('portal.user.evaluation.report');
            Route::post('/evaluation/details', [EvaluationController::class, 'user_evaluation_details'])->name('portal.user.evaluation.details');
            Route::post('/evaluation/question/single', [EvaluationController::class, 'user_evaluation_question_single'])->name('portal.user.evaluation.question.single');
            Route::post('/evaluation/question/single/chatGPT', [EvaluationController::class, 'user_evaluation_question_single_chatGPT'])->name('portal.user.evaluation.question.single.chatGPT');
            Route::post('/evaluation/question/single/update', [EvaluationController::class, 'user_evaluation_question_single_update'])->name('portal.user.evaluation.question.single.update');
            Route::get('/evaluation/certificate/{evaluation_id}', [EvaluationController::class, 'user_evaluation_certificate'])->name('portal.user.evaluation.certificate');
        });

        // API routes for risk-evaluation related actions
        Route::group(['prefix' => 'risk/evaluation'], function () {
            Route::post('/submit', [EvaluationController::class, 'risk_evaluation'])->name('portal.evaluation.submit');
            Route::post('/technical/questions', [EvaluationController::class, 'evaluation_technical_questions'])->name('portal.evaluation.technical.questions');
            Route::post('/question/groups', [EvaluationController::class, 'evaluation_question_groups'])->name('portal.evaluation.question.groups');
            Route::post('/fd/sector/details', [EvaluationController::class, 'evaluation_fd_sector_single'])->name('portal.evaluation.fd.sector.single');
            Route::post('/fd/questions', [EvaluationController::class, 'evaluation_fd_questions'])->name('portal.evaluation.fd.questions');
            Route::post('/non-technical/questions', [EvaluationController::class, 'evaluation_non_technical_questions'])->name('portal.evaluation.non.technical.questions');
            Route::post('/fd/sectors', [EvaluationController::class, 'evaluation_fd_sectors'])->name('portal.evaluation.fd.sectors');
            Route::post('/sectors', [EvaluationController::class, 'evaluation_sectors'])->name('portal.evaluation.sectors');
        });

        // Awareness Evaluation routes
        Route::group(['prefix' => 'awareness'], function () {
            Route::get('/evaluations', [AwarenessController::class, 'aware_evaluations'])->name('portal.awareness.evaluations');
            Route::get('courses', [AwarenessController::class, 'getCourses']);
            Route::get('single/{id}', [AwarenessController::class, 'getSingleCourse']);
            Route::get('single/{id}/certificate', [AwarenessController::class, 'course_certificate']);
            Route::prefix('{awareness_id}/topic')->group(function () {
                Route::get('list', [AwarenessController::class, 'topic_list']);
                Route::prefix('{topic_id}/lesson')->group(function () {
                    Route::get('single/{id}', [AwarenessController::class, 'topic_lesson_single']);
                    Route::get('single/{id}/complete', [AwarenessController::class, 'topic_lesson_single_complete']);
                    Route::post('single/{id}/quiz/submit', [AwarenessController::class, 'topic_lesson_single_quiz_submit']);
                });
            });
        });
    });

    // User Consultancy Routes (MOVED HERE - INSIDE auth:api middleware)
    Route::prefix('secure/user/consultancy')->group(function () {
        // Get available consultancy slots for booking
        Route::post('slots/available', [UserConsultancyController::class, 'getAvailableSlots']);
        
        // Book a consultancy session
        Route::post('book', [UserConsultancyController::class, 'bookSession']);
        
        // Get user's bookings
        Route::post('my-bookings', [UserConsultancyController::class, 'getMyBookings']);
        
        // Cancel a booking
        Route::post('cancel', [UserConsultancyController::class, 'cancelBooking']);
        
        // Get user's consultancy usage/remaining hours
        Route::get('usage', [UserConsultancyController::class, 'getMyUsage']);
        
        // Get upcoming sessions
        Route::get('upcoming', [UserConsultancyController::class, 'getUpcomingSessions']);
    });
});