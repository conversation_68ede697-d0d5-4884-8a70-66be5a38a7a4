<?php

namespace App\Repositories\Admin\AwarenessManagement;

use App\Interfaces\Admin\AwarenessManagement\AwarenessTopicRepositoryInterface;
use Illuminate\Http\Request;
use App\Repositories\Admin\AwarenessManagement\Traits\AwarenessHierarchyTrait;
use App\Services\Admin\AwarenessManagement\TopicValidator;

class AwarenessTopicRepository implements AwarenessTopicRepositoryInterface
{
    use AwarenessHierarchyTrait;
    protected $topicValidator;

    public function __construct(TopicValidator $topicValidator)
    {
        $this->topicValidator = $topicValidator;
    }

    public function list(Request $request, string $awarenessId): array
    {
        try {
            $awareness = $this->getAwareness($awarenessId);

            $query = $awareness->topics()->with(['lessons' => function ($q) {
                    $q->orderBy('order', 'asc');
            }])->orderBy('order', 'asc');

            if ($request->has('keyword')) {
                $query->where('title', 'like', '%' . $request->keyword . '%');
            }

            $topics = $query->get();

            return ['status' => 200, 'data' => $topics->toArray()];
        } catch (\Exception $e) {
            return ['status' => 500, 'message' => $e->getMessage()];
        }
    }

    public function create(Request $request, string $awarenessId): array
    {
        try {
            $awareness = $this->getAwareness($awarenessId);
            $validatedData = $this->topicValidator->validate($request, true);

            $validatedData['order'] = $awareness->topics()->max('order') + 1 ?? 1;
            $topic = $awareness->topics()->create($validatedData);

            return ['status' => 200, 'message' => 'Topic created successfully'];
        } catch (\Exception $e) {
            return ['status' => 500, 'error' => $e->getMessage()];
        }
    }

    public function update(Request $request, string $awarenessId, string $topicId): array
    {
        try {
            $topic = $this->getTopic($awarenessId, $topicId);
            $validatedData = $this->topicValidator->validate($request, false);

            $topic->update($validatedData);

            return ['status' => 200, 'message' => 'Topic updated successfully'];
        } catch (\Exception $e) {
            return ['status' => 500, 'message' => $e->getMessage()];
        }
    }

    public function delete(Request $request, string $awarenessId, string $topicId): array
    {
        try {
            $topic = $this->getTopic($awarenessId, $topicId);
            $lessons = $topic->lessons()->get();
            foreach ($lessons as $lesson) {
                $lesson->questions()->delete();
            }
            $topic->lessons()->delete();
            $topic->delete();

            return ['status' => 200, 'message' => 'Topic and its lessons deleted successfully'];
        } catch (\Exception $e) {
            return ['status' => 500, 'error' => $e->getMessage()];
        }
    }
}