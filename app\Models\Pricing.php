<?php

namespace App\Models;

use Jen<PERSON>gers\Mongodb\Eloquent\Model;

class Pricing extends Model
{
    protected $connection = 'mongodb';
    protected $collection = 'pricing_packages';

    protected $fillable = [
        'package_name',
        'package_level',
        'package_price',
        'currency',
        'risk_evaluation',
        'fair_decision_analysis',
        'in_person_services',
        'training',
        'online_consultancy_hours',
        'stripe_product_id',
        'stripe_price_id',
    ];

    protected $casts = [
        'risk_evaluation' => 'array',
        'fair_decision_analysis' => 'array',
        'in_person_services' => 'array',
        'training' => 'boolean'
    ];
}

