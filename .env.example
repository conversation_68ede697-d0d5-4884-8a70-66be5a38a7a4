APP_NAME=RAIDOT
APP_ENV=local
APP_KEY=base64:sS+JY7ZkvQA2ERWpsnX35Hhfk6xTUV+nmL60jc3XW6E=
APP_DEBUG=true
APP_URL=http://localhost:8000
FRONTEND_URL=http://localhost:5173

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mongodb
DB_HOST=127.0.0.1
DB_PORT=27017
DB_DATABASE=mot4ai
DB_USERNAME=
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=smtp.sendgrid.net
MAIL_PORT=587
MAIL_USERNAME=apikey
MAIL_PASSWORD=*********************************************************************
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"


STRIPE_PUBLISH_KEY=pk_test_51OAabgCnUIZ0oKB6RcfI54aEPwJA45NjDT4yNcDh5pbCrWQJZXsJTqrXwvDjj6NW5T8YVdbo4La6LMpYNrsWEbx000Gu2YDEpX
STRIPE_SECRET_KEY=sk_test_51OAabgCnUIZ0oKB6NXw00BpdNPaj8ZPkyysKXo1CQt9hph4Ev9SjninXmGNUMtIKdzwHcQtcmfbK31tIICUBKy1e00Ek55xOt1
STRIPE_PREMIUM_PRICE=price_1OBwmrCnUIZ0oKB6rSdUQut7

RECAPTCHA_PROJECT_ID=raidot-1712657522991
RECAPTCHA_SITE_KEY=6LfqoLUpAAAAAKuFG5jpdD5v9p5PTGphkgs0QoN0
RECAPTCHA_SECRET_KEY=6LfqoLUpAAAAABq0pw3LNpceYOZQkK_9LTHWx2Do


JWT_SECRET=UXZTQ3bfRY8mC6L2qeIGWz4i1GY3OOsY1vV7DScAnsnvYhPhXTCWzVcyHc8wPIeb
