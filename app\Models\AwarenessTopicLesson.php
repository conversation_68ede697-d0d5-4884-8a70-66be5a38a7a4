<?php

namespace App\Models;

use <PERSON><PERSON><PERSON>\Mongodb\Eloquent\Model as Eloquent;

class AwarenessTopicLesson extends Eloquent
{
    protected $connection = 'mongodb';
    protected $collection = 'awareness_topic_lessons';
    protected $fillable = [
        'topic_id',
        'order',
        'title',
        'description',
        'lesson_type'
    ];
    protected $hidden = [
        'updated_at',
        'created_at'
    ];
    public $timestamps = true;

    public function topic()
    {
        return $this->belongsTo(AwarenessTopic::class, 'topic_id');
    }

    public function questions()
    {
        return $this->hasMany(AwarenessTopicLessonQuestion::class, 'lesson_id');
    }
}