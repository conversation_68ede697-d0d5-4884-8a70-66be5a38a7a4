<?php

namespace App\Services\Admin\AwarenessManagement;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class QuestionValidator
{
    protected $rules = [
        'question' => 'required|string',
        'option_1' => 'required|string',
        'option_2' => 'required|string',
        'option_3' => 'required|string',
        'option_4' => 'required|string',
        'answer' => 'required|string',
        'order' => 'sometimes|integer',
    ];

    public function validate(Request $request): array
    {
        $validator = Validator::make($request->all(), $this->rules);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return $request->only(array_keys($this->rules));
    }
}