<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateNotificationsCollection extends Migration
{
    public function up()
    {
        // For MongoDB, we don't need to create the collection explicitly
        // But we can set up indexes for better performance
        
        // You can run these MongoDB commands directly in your database:
        /*
        db.notifications.createIndex({ "user_id": 1 })
        db.notifications.createIndex({ "created_at": -1 })
        db.notifications.createIndex({ "read_at": 1 })
        db.notifications.createIndex({ "type": 1 })
        db.notifications.createIndex({ "user_id": 1, "read_at": 1 })
        */
    }

    public function down()
    {
        // Drop the collection if needed
        // db.notifications.drop()
    }
}
