<?php

namespace App\Repositories\Front;

use App\Models\Awareness;
use App\Models\AwarenessTopic;
use App\Models\AwarenessTopicLesson;
use App\Models\AwarenessTopicLessonQuestion;
use App\Models\AwarenessTopicLessonRead;
use App\Models\AwarenessCertificates;
use App\Models\PackagePurchase;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use App\Models\User;
use App\Models\CertificateSettings;
use Barryvdh\DomPDF\Facade\Pdf;
use App\Models\Pricing;

class AwarenessRepository
{
    private static function checkCourseAccess($awareness, $user = null)
    {
        if (!$user) {
            $user = Auth::user();
        }

        if (!$user) {
            return ['allowed' => false, 'error' => 'Unauthorized'];
        }

        $userPackage = PackagePurchase::with('package')
            ->where('user_id', $user->_id)
            ->where('status', 'active')
            ->first();

        if (!$userPackage || !$userPackage->package) {
            return ['allowed' => false, 'error' => 'No active subscription found'];
        }

        if (!$awareness->relationLoaded('package')) {
            $awareness->load('package');
        }

        if ($awareness->package) {
            $userPackageLevel = $userPackage->package->package_level;
            $coursePackageLevel = $awareness->package->package_level;

            if ($coursePackageLevel > $userPackageLevel) {
                return ['allowed' => false, 'error' => 'This course requires a higher subscription level'];
            }
        }

        return ['allowed' => true];
    }

    public static function check_certificate_eligibility($id): bool
    {
        try {
            $certificate_eligibility = false;
            $awareness = Awareness::where('_id', $id)->first();
            if ($awareness == null) return $certificate_eligibility;

            $user_id = Auth::id();
            if (!$user_id) return $certificate_eligibility;

            $topics = $awareness->topics()->with(['lessons' => function ($q) {
                $q->orderBy('order', 'asc');
            }])->orderBy('order', 'asc')->get()->toArray();

            $totalLessons = 0;
            $completedLessons = 0;
            $hasCompletedFinalExam = false;

            foreach ($topics as $topic) {
                if (isset($topic['lessons'])) {
                    foreach ($topic['lessons'] as $lesson) {
                        $totalLessons++;

                        $reading = AwarenessTopicLessonRead::where('awareness_id', $awareness->_id)
                            ->where('topic_id', $topic['_id'])
                            ->where('lesson_id', $lesson['_id'])
                            ->where('user_id', $user_id)
                            ->where('is_guest', 0)
                            ->first();

                        if ($reading && $reading->completed) {
                            $completedLessons++;

                            if (($lesson['lesson_type'] === 'final_exam' || $reading->is_final_exam) &&
                                $reading->score >= 80) {
                                $hasCompletedFinalExam = true;
                            }
                        }
                    }
                }
            }

            if ($totalLessons > 0 && $completedLessons === $totalLessons && $hasCompletedFinalExam) {
                $certificate_eligibility = true;
            }

            return $certificate_eligibility;
        } catch (\Exception $e) {
            Log::error('Certificate eligibility check error: ' . $e->getMessage());
            return false;
        }
    }

    public static function course_certificate($request, $id)
    {
        try {
            $awareness = Awareness::getWithAllData($id);
            if (!$awareness) {
                return "Invalid request - course not found.";
            }

            $accessCheck = self::checkCourseAccess($awareness);
            if (!$accessCheck['allowed']) {
                return "Access denied: " . $accessCheck['error'];
            }

            $certificate_eligibility = self::check_certificate_eligibility($id);
            $user = User::where('_id', Auth::id())->first();

            if (!$user) {
                return "Invalid request - user not found.";
            }

            if ($user->subscription_type == 'premium' && $certificate_eligibility) {
                $certificate_data = [];
                $certificate_data['user'] = $user;
                $certificate_data['cert_settings'] = CertificateSettings::where('type', 'awareness')->first();

                if ($certificate_data['cert_settings'] != null) {
                    $certificate_data['cert_settings'] = $certificate_data['cert_settings']->toArray();
                    $certificate_data['cert_settings']['description_letter'] = str_replace('[--Evaluation--]', '<strong>' . $awareness['title'] . '</strong>', $certificate_data['cert_settings']['description_letter']);
                    $certificate_data['cert_settings']['description_letter'] = str_replace('[--Date--]', '<strong>' . date('d M, Y') . '</strong>', $certificate_data['cert_settings']['description_letter']);
                }

                if (!file_exists(public_path('storage/media/certificate'))) {
                    mkdir(public_path('storage/media/certificate'), 0777, true);
                }

                $pdf = Pdf::loadView('pdf.certificate_awareness_evaluation', $certificate_data);
                $pdf->setPaper('a3', 'landscape');
                $pdfName = 'Certificate-' . $awareness->title . '.pdf';
                $pdfPath = public_path('storage/media/certificate/' . $pdfName);
                $pdf->save($pdfPath);

                $AwarenessCertificate = AwarenessCertificates::where('awareness_id', $awareness->_id)
                    ->where('user_id', $user->_id)
                    ->first();

                if ($AwarenessCertificate == null) {
                    AwarenessCertificates::create([
                        'awareness_id' => $awareness->_id,
                        'user_id' => $user->_id,
                        'full_name' => $user->name,
                        'email' => $user->email,
                        'file_path' => $pdfName
                    ]);
                }

                return response()->file($pdfPath, [
                    'Content-Type' => 'application/pdf',
                    'Content-Disposition' => 'inline; filename="' . $pdfName . '"'
                ]);
            } elseif ($user->subscription_type !== 'premium') {
                return "This request is invalid for Free Users! Please upgrade to Premium.";
            } else {
                return "Certificate not available. You must complete all lessons and pass the final exam with at least 80% to earn a certificate.";
            }
        } catch (\Exception $e) {
            Log::error('Certificate generation error: ' . $e->getMessage());
            return "An error occurred while generating the certificate.";
        }
    }

    public static function topic_list(Request $request, $awareness_id): array
    {
        try {
            $awareness_session_id = Auth::id() ?? Session::get('awareness_session_id');
            $is_guest = Auth::check() ? 0 : 1;

            $awareness = Awareness::with('package')->find($awareness_id);
            if (!$awareness) {
                return ['status' => 404, 'error' => 'Awareness course not found.'];
            }

            $accessCheck = self::checkCourseAccess($awareness);
            if (!$accessCheck['allowed']) {
                return ['status' => 403, 'error' => $accessCheck['error']];
            }

            $keyword = $request->keyword ?? null;

            $query = $awareness->topics()->with(['lessons' => function ($q) {
                $q->orderBy('order', 'asc');
            }])->orderBy('order', 'asc');

            if (!empty($keyword)) {
                $query->where('title', 'LIKE', '%' . $keyword . '%');
            }

            $topics = $query->get()->toArray();

            $lastRead = 1;
            foreach ($topics as &$topic) {
                foreach ($topic['lessons'] as &$lesson) {
                    $lesson['completed'] = AwarenessTopicLessonRead::where('awareness_id', $awareness_id)
                        ->where('topic_id', $topic['_id'])
                        ->where('lesson_id', $lesson['_id'])
                        ->where('user_id', $awareness_session_id)
                        ->where('is_guest', $is_guest)
                        ->exists() ? 1 : 0;

                    $lesson['readable'] = 0;
                    if ($lesson['completed'] == 0 && $lastRead == 1) {
                        $lastRead = 0;
                        $lesson['readable'] = 1;
                    } elseif ($lesson['completed'] == 1) {
                        $lesson['readable'] = 1;
                    }

                    $lesson['is_quiz'] = ($lesson['lesson_type'] === 'quiz' || $lesson['lesson_type'] === 'final_exam') ? '1' : '0';
                    $lesson['is_final_exam'] = ($lesson['lesson_type'] === 'final_exam') ? '1' : '0';
                }
            }

            return ['status' => 200, 'data' => $topics];

        } catch (\Exception $e) {
            Log::error('Error in topic_list: ' . $e->getMessage());
            return ['status' => 500, 'error' => $e->getMessage()];
        }
    }

    public static function topic_lesson_single($request, $awareness_id, $topic_id, $id): array
    {
        try {
            $awareness_session_id = Auth::id() ?? Session::get('awareness_session_id');
            $is_guest = Auth::check() ? 0 : 1;

            $awareness = Awareness::with('package')->find($awareness_id);
            if (!$awareness) {
                return ['status' => 404, 'error' => 'Awareness course not found.'];
            }

            $accessCheck = self::checkCourseAccess($awareness);
            if (!$accessCheck['allowed']) {
                return ['status' => 403, 'error' => $accessCheck['error']];
            }

            $topic = $awareness->topics()->find($topic_id);
            if (!$topic) {
                return ['status' => 404, 'error' => 'Topic not found.'];
            }

            $lesson = $topic->lessons()->with('questions')->find($id);
            if (!$lesson) {
                return ['status' => 404, 'error' => 'Lesson not found.'];
            }

            $data = $lesson->toArray();

            $ReadHistory = AwarenessTopicLessonRead::where('awareness_id', $awareness_id)
                ->where('topic_id', $topic_id)
                ->where('lesson_id', $id)
                ->where('user_id', $awareness_session_id)
                ->where('is_guest', $is_guest)
                ->first();

            $data['completed'] = $ReadHistory ? 1 : 0;

            $data['is_quiz'] = ($lesson->lesson_type === 'quiz' || $lesson->lesson_type === 'final_exam') ? '1' : '0';
            $data['is_final_exam'] = ($lesson->lesson_type === 'final_exam') ? '1' : '0';

            if ($data['is_quiz'] === '1') {
                foreach ($data['questions'] as &$question) {
                    $question['answer'] = '';
                }

                $quiz_result = null;
                if ($ReadHistory && $ReadHistory->submissions) {
                    $submissions = array_values(json_decode($ReadHistory->submissions, true));
                    $quiz_result = [
                        'score' => $ReadHistory->score ?? 0,
                        'submissions' => $submissions
                    ];
                }

                $rv = [
                    'lesson' => $data,
                    'quiz_result' => $quiz_result
                ];
            } else {
                $rv = [
                    'lesson' => $data
                ];
            }

            return ['status' => 200, 'data' => $rv];

        } catch (\Exception $e) {
            Log::error('Error in topic_lesson_single: ' . $e->getMessage());
            return ['status' => 500, 'error' => $e->getMessage()];
        }
    }

    public static function topic_lesson_single_complete($request, $awareness_id, $topic_id, $id): array
    {
        try {
            $awareness_session_id = Auth::id() ?? Session::get('awareness_session_id');
            $is_guest = Auth::check() ? 0 : 1;

            $awareness = Awareness::with('package')->find($awareness_id);
            if (!$awareness) {
                return ['status' => 404, 'error' => 'Awareness course not found.'];
            }

            $accessCheck = self::checkCourseAccess($awareness);
            if (!$accessCheck['allowed']) {
                return ['status' => 403, 'error' => $accessCheck['error']];
            }

            $topic = $awareness->topics()->find($topic_id);
            if (!$topic) {
                return ['status' => 404, 'error' => 'Topic not found.'];
            }

            $lesson = $topic->lessons()->find($id);
            if (!$lesson) {
                return ['status' => 404, 'error' => 'Lesson not found.'];
            }

            $existingRead = AwarenessTopicLessonRead::where('awareness_id', $awareness_id)
                ->where('topic_id', $topic_id)
                ->where('lesson_id', $id)
                ->where('user_id', $awareness_session_id)
                ->where('is_guest', $is_guest)
                ->first();

            if (!$existingRead) {
                AwarenessTopicLessonRead::create([
                    'awareness_id' => $awareness_id,
                    'topic_id' => $topic_id,
                    'lesson_id' => $id,
                    'user_id' => $awareness_session_id,
                    'is_guest' => $is_guest,
                    'completed' => 1
                ]);
            } else {
                $existingRead->completed = 1;
                $existingRead->save();
            }

            return ['status' => 200, 'msg' => 'Lesson completed successfully.'];

        } catch (\Exception $e) {
            Log::error('Error in topic_lesson_single_complete: ' . $e->getMessage());
            return ['status' => 500, 'error' => $e->getMessage()];
        }
    }

    public static function topic_lesson_single_quiz_submit($request, $awareness_id, $topic_id, $id): array
    {
        try {
            $awareness_session_id = Auth::id() ?? Session::get('awareness_session_id');
            $is_guest = Auth::check() ? 0 : 1;

            $awareness = Awareness::with('package')->find($awareness_id);
            if (!$awareness) {
                return ['status' => 404, 'error' => 'Awareness course not found.'];
            }

            $accessCheck = self::checkCourseAccess($awareness);
            if (!$accessCheck['allowed']) {
                return ['status' => 403, 'error' => $accessCheck['error']];
            }

            $validator = Validator::make($request->all(), [
                'answers' => 'required|array'
            ]);

            if ($validator->fails()) {
                return ['status' => 422, 'error' => $validator->errors()];
            }

            $answers = $request->input('answers');

            $lesson = AwarenessTopicLesson::with('questions')->find($id);
            if (!$lesson) {
                return ['status' => 404, 'error' => 'Lesson not found.'];
            }

            $questions = $lesson->questions;
            $totalQuestions = count($questions);
            $correctAnswers = 0;
            $submissions = [];

            foreach ($questions as $question) {
                $userAnswer = $answers[$question->_id] ?? '';
                $isCorrect = strtolower($userAnswer) === strtolower($question->correct_answer);

                if ($isCorrect) {
                    $correctAnswers++;
                }

                $submissions[$question->_id] = [
                    'question' => $question->question,
                    'user_answer' => $userAnswer,
                    'correct_answer' => $question->correct_answer,
                    'is_correct' => $isCorrect
                ];
            }

            $score = $totalQuestions > 0 ? round(($correctAnswers / $totalQuestions) * 100, 2) : 0;

            $checkQuiz = AwarenessTopicLessonRead::where('awareness_id', $awareness_id)
                ->where('topic_id', $topic_id)
                ->where('lesson_id', $id)
                ->where('user_id', $awareness_session_id)
                ->where('is_guest', $is_guest)
                ->first();

            if (!$checkQuiz) {
                $checkQuiz = AwarenessTopicLessonRead::create([
                    'awareness_id' => $awareness_id,
                    'topic_id' => $topic_id,
                    'lesson_id' => $id,
                    'user_id' => $awareness_session_id,
                    'is_guest' => $is_guest
                ]);
            }

            $checkQuiz->is_final_exam = ($lesson->lesson_type === 'final_exam') ? 1 : 0;
            $checkQuiz->completed = 1;
            $checkQuiz->score = $score;
            $checkQuiz->submissions = json_encode($submissions, true);
            $checkQuiz->save();

            $rv = [
                'score' => $score,
                'submissions' => array_values($submissions)
            ];
            return ['status' => 200, 'data' => $rv];

        } catch (\Exception $e) {
            Log::error('Error in topic_lesson_single_quiz_submit: ' . $e->getMessage());
            return ['status' => 500, 'error' => $e->getMessage()];
        }
    }

    public static function aware_evaluations($request): array
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return ['status' => 401, 'error' => 'Unauthorized'];
            }

            $userPackage = PackagePurchase::with('package')
                ->where('user_id', $user->_id)
                ->where('status', 'active')
                ->first();

            if (!$userPackage || !$userPackage->package) {
                return ['status' => 403, 'error' => 'No active subscription found'];
            }

            $userPackageLevel = $userPackage->package->package_level;

            $aware_evaluation_ids = AwarenessTopicLessonRead::where('user_id', Auth::id())
                ->where('is_guest', 0)
                ->pluck('awareness_id')
                ->toArray();

            $aware_evaluations = Awareness::with('package')
                ->whereIn('_id', $aware_evaluation_ids)
                ->whereHas('package', function($query) use ($userPackageLevel) {
                    $query->where('package_level', '<=', $userPackageLevel);
                })
                ->orderBy('created_at', 'desc')
                ->get()
                ->toArray();

            foreach ($aware_evaluations as &$d) {
                $d['title_short'] = substr($d['title'], 0, 50);
                if(strlen($d['title']) > 50){
                    $d['title_short'] .= '...';
                }

                $d['description_short'] = substr($d['description'], 0, 150);
                if(strlen($d['description']) > 150){
                    $d['description_short'] .= '...';
                }
            }

            return ['status' => 200, 'data' => $aware_evaluations];
        } catch (\Exception $e) {
            Log::error('Error in aware_evaluations: ' . $e->getMessage());
            return ['status' => 500, 'error' => $e->getMessage()];
        }
    }

    public static function getCourses(Request $request): array
    {
        try {
            $user = Auth::user();
            $userPackage = PackagePurchase::where('user_id', $user->_id)->where('status', 'active')->first();
            $pricingPackage = Pricing::where('_id', $userPackage->pricing_package_id)->first();
            $userPackageLevel = $pricingPackage->package_level;

            $courses = Awareness::with('package')->get();
            $coursesArray = $courses->map(function ($course) use ($userPackageLevel) {
                return [
                    '_id' => $course->_id,
                    'title' => $course->title,
                    'description' => $course->description ?? '',
                    'banner_full_path' => $course->banner_full_path,
                    'created_at' => $course->created_at,
                    'accessibility' => $course->package && $course->package->package_level <= $userPackageLevel,
                    'package' => $course->package ? $course->package->toArray() : null,
                ];
            })->toArray();
            return ['status' => 200, 'data' => $coursesArray];
        } catch (\Exception $e) {
            Log::error('Error in getCourses: ' . $e->getMessage());
            return ['status' => 500, 'error' => $e->getMessage()];
        }
    }

    public static function getSingleCourse($request, $id): array
    {
        try {
            $awareness = Awareness::getWithAllDataExceptAnswer($id);
            if ($awareness == null) return ['status' => 404, 'error' => 'No awareness course found.'];
            return ['status' => 200, 'data' => $awareness];
        } catch (\Exception $e) {
            return ['status' => 500, 'error' => $e->getMessage()];
        }
    }
}