<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Evaluation;
use App\Models\Awareness;
use App\Models\EvaluationSectors;
use App\Models\FairDecisionSectors;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class SearchController extends Controller
{
    /**
     * Search across evaluations and awareness courses
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function search(Request $request)
    {
        try {
            $user = Auth::user();
            $input = $request->input();
            
            $validator = Validator::make($input, [
                'query' => 'required|string|min:1',
                'types' => 'nullable|array',
                'types.*' => 'in:evaluations,courses',
                'limit' => 'nullable|integer|min:1|max:50'
            ]);

            if ($validator->fails()) {
                return response()->json(['status' => 422, 'error' => $validator->errors()], 422);
            }

            $query = $input['query'];
            $types = $input['types'] ?? ['evaluations', 'courses'];
            $limit = $input['limit'] ?? 10;
            
            $results = [
                'evaluations' => [],
                'courses' => [],
                'total' => 0
            ];

            // Search evaluations
            if (in_array('evaluations', $types)) {
                $evaluations = Evaluation::where('user_id', $user->_id)
                    ->where(function ($q) use ($query) {
                        $q->where('project.name', 'like', '%' . $query . '%')
                          ->orWhere('project.desc', 'like', '%' . $query . '%')
                          ->orWhere('title', 'like', '%' . $query . '%');
                    })
                    ->orderBy('created_at', 'desc')
                    ->limit($limit)
                    ->get();

                $results['evaluations'] = $evaluations->map(function ($evaluation) {
                    $sectorInfo = null;
                    $subSectorInfo = null;
                    
                    // Get sector information based on category
                    if (isset($evaluation->evaluation_sector) && $evaluation->evaluation_sector > 0) {
                        if ($evaluation->category == 'eta') {
                            $sectorInfo = EvaluationSectors::where('uid', $evaluation->evaluation_sector)->first();
                            if ($evaluation->evaluation_sub_sector > 0) {
                                $subSectorInfo = EvaluationSectors::where('uid', $evaluation->evaluation_sub_sector)->first();
                            }
                        } elseif ($evaluation->category == 'eta-fd') {
                            $sectorInfo = FairDecisionSectors::where('uid', $evaluation->evaluation_sector)->first();
                            if ($evaluation->evaluation_sub_sector > 0) {
                                $subSectorInfo = FairDecisionSectors::where('uid', $evaluation->evaluation_sub_sector)->first();
                            }
                        }
                    }
                    
                    return [
                        '_id' => $evaluation->_id,
                        'project' => $evaluation->project ?? ['name' => $evaluation->title ?? 'Untitled'],
                        'category' => $evaluation->category,
                        'sector' => $sectorInfo ? $sectorInfo->title : null,
                        'sub_sector' => $subSectorInfo ? $subSectorInfo->title : null,
                        'risk_level' => $evaluation->report['finalRiskLevel'] ?? null,
                        'created_at' => $evaluation->created_at,
                        'updated_at' => $evaluation->updated_at
                    ];
                });
            }

            // Search courses (awareness)
            if (in_array('courses', $types)) {
                $courses = Awareness::where(function ($q) use ($query) {
                        $q->where('title', 'like', '%' . $query . '%')
                          ->orWhere('description', 'like', '%' . $query . '%');
                    })
                    ->orderBy('created_at', 'desc')
                    ->limit($limit)
                    ->get();

                $results['courses'] = $courses->map(function ($course) {
                    return [
                        '_id' => $course->_id,
                        'title' => $course->title,
                        'description' => $course->description,
                        'type' => $course->type,
                        'category' => $course->category,
                        'certificate' => $course->certificate,
                        'banner_full_path' => $course->banner_full_path,
                        'created_at' => $course->created_at
                    ];
                });
            }

            $results['total'] = count($results['evaluations']) + count($results['courses']);

            return response()->json([
                'status' => 200,
                'data' => $results,
                'msg' => 'Search results retrieved successfully'
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 500,
                'error' => 'Failed to search: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get search suggestions
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function suggestions(Request $request)
    {
        try {
            $user = Auth::user();
            
            // Get recent evaluation names for suggestions
            $recentEvaluations = Evaluation::where('user_id', $user->_id)
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get()
                ->map(function ($evaluation) {
                    return $evaluation->project['name'] ?? $evaluation->title ?? 'Untitled';
                })
                ->filter()
                ->values();

            // Get popular course names
            $popularCourses = Awareness::orderBy('created_at', 'desc')
                ->limit(5)
                ->pluck('title')
                ->filter()
                ->values();

            $suggestions = $recentEvaluations->merge($popularCourses)->unique()->take(10);

            return response()->json([
                'status' => 200,
                'data' => $suggestions,
                'msg' => 'Search suggestions retrieved successfully'
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 500,
                'error' => 'Failed to get suggestions: ' . $e->getMessage()
            ], 500);
        }
    }
}