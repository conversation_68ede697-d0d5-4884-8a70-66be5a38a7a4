<?php

namespace App\Http\Controllers\Admin\AwarenessManagement;

use App\Http\Controllers\Controller;
use App\Interfaces\Admin\AwarenessManagement\AwarenessTopicLessonRepositoryInterface;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class AwarenessTopicLessonController extends Controller
{
    private $awarenessTopicLessonRepository;

    public function __construct(AwarenessTopicLessonRepositoryInterface $awarenessTopicLessonRepository)
    {
        $this->awarenessTopicLessonRepository = $awarenessTopicLessonRepository;
    }

    public function create(Request $request, string $awarenessId, string $topicId): JsonResponse
    {
        $response = $this->awarenessTopicLessonRepository->create($request, $awarenessId, $topicId);
        return response()->json($response, $response['status']);
    }

    public function single(Request $request, string $awarenessId, string $topicId, string $lessonId): JsonResponse
    {
        $response = $this->awarenessTopicLessonRepository->single($request, $awarenessId, $topicId, $lessonId);
        return response()->json($response, $response['status']);
    }

    public function update(Request $request, string $awarenessId, string $topicId, string $lessonId): JsonResponse
    {
        $response = $this->awarenessTopicLessonRepository->update($request, $awarenessId, $topicId, $lessonId);
        return response()->json($response, $response['status']);
    }

    public function delete(Request $request, string $awarenessId, string $topicId, string $lessonId): JsonResponse
    {
        $response = $this->awarenessTopicLessonRepository->delete($request, $awarenessId, $topicId, $lessonId);
        return response()->json($response, $response['status']);
    }
}
