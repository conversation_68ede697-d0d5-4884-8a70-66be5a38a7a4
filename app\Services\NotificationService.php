<?php

namespace App\Services;

use App\Models\Notification;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class NotificationService
{
    /**
     * Create a new notification
     *
     * @param string $userId
     * @param string $title
     * @param string $message
     * @param string $type
     * @param string|null $actionUrl
     * @param array $data
     * @return Notification|false
     */
    public static function create($userId, $title, $message, $type = 'default', $actionUrl = null, $data = [])
    {
        try {
            // Validate user exists before creating notification
            if (!User::where('_id', $userId)->exists()) {
                Log::warning("Attempted to create notification for non-existent user: {$userId}");
                return false;
            }

            return Notification::create([
                'user_id' => $userId,
                'title' => trim($title),
                'message' => trim($message),
                'type' => $type,
                'action_url' => $actionUrl,
                'data' => is_array($data) ? $data : [],
                'read_at' => null
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to create notification: ' . $e->getMessage(), [
                'user_id' => $userId,
                'title' => $title,
                'type' => $type,
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Send evaluation completion notification
     *
     * @param string $userId
     * @param string $evaluationName
     * @param string $evaluationId
     * @return Notification|false
     */
    public static function evaluationCompleted($userId, $evaluationName, $evaluationId)
    {
        return self::create(
            $userId,
            'Evaluation Completed',
            "Your '{$evaluationName}' evaluation has been completed successfully.",
            'evaluation_completed',
            "/portal/evaluation/report/{$evaluationId}",
            ['evaluation_id' => $evaluationId]
        );
    }

    /**
     * Send evaluation submission notification
     *
     * @param string $userId
     * @param string $evaluationName
     * @param string $evaluationId
     * @return Notification|false
     */
    public static function evaluationSubmitted($userId, $evaluationName, $evaluationId)
    {
        return self::create(
            $userId,
            'Evaluation Submitted',
            "Your '{$evaluationName}' evaluation has been submitted and is being processed.",
            'evaluation_submitted',
            "/portal/evaluation/report/{$evaluationId}",
            ['evaluation_id' => $evaluationId]
        );
    }

    /**
     * Send course completion notification
     *
     * @param string $userId
     * @param string $courseName
     * @param string $courseId
     * @return Notification|false
     */
    public static function courseCompleted($userId, $courseName, $courseId)
    {
        return self::create(
            $userId,
            'Course Completed',
            "Congratulations! You've completed the '{$courseName}' course.",
            'course_completed',
            "/portal/awareness/{$courseId}",
            ['course_id' => $courseId]
        );
    }

    /**
     * Send lesson completion notification
     *
     * @param string $userId
     * @param string $lessonName
     * @param string $courseId
     * @param string $lessonId
     * @return Notification|false
     */
    public static function lessonCompleted($userId, $lessonName, $courseId, $lessonId)
    {
        return self::create(
            $userId,
            'Lesson Completed',
            "You've successfully completed the lesson '{$lessonName}'.",
            'lesson_completed',
            "/portal/awareness/{$courseId}/lesson/{$lessonId}",
            ['course_id' => $courseId, 'lesson_id' => $lessonId]
        );
    }

    /**
     * Send certificate ready notification
     *
     * @param string $userId
     * @param string $type
     * @param string $name
     * @param string $downloadUrl
     * @return Notification|false
     */
    public static function certificateReady($userId, $type, $name, $downloadUrl)
    {
        return self::create(
            $userId,
            'Certificate Ready',
            "Your certificate for '{$name}' is ready for download.",
            'certificate_ready',
            $downloadUrl,
            ['type' => $type, 'name' => $name]
        );
    }

    /**
     * Send subscription expiration notification
     *
     * @param string $userId
     * @param int $daysLeft
     * @return Notification|false
     */
    public static function subscriptionExpiring($userId, $daysLeft)
    {
        return self::create(
            $userId,
            'Subscription Expiring',
            "Your subscription will expire in {$daysLeft} days. Renew now to continue enjoying premium features.",
            'subscription_expiring',
            '/subscribe',
            ['days_left' => $daysLeft]
        );
    }

    /**
     * Send subscription renewal notification
     *
     * @param string $userId
     * @param string $planName
     * @return Notification|false
     */
    public static function subscriptionRenewed($userId, $planName)
    {
        return self::create(
            $userId,
            'Subscription Renewed',
            "Your {$planName} subscription has been successfully renewed.",
            'subscription_renewed',
            '/portal/profile',
            ['plan_name' => $planName]
        );
    }

    /**
     * Send subscription activated notification
     *
     * @param string $userId
     * @param string $planName
     * @return Notification|false
     */
    public static function subscriptionActivated($userId, $planName)
    {
        return self::create(
            $userId,
            'Subscription Activated',
            "Welcome to {$planName}! Your premium features are now active.",
            'subscription_activated',
            '/portal/profile',
            ['plan_name' => $planName]
        );
    }

    /**
     * Send system update notification
     *
     * @param string $userId
     * @param string $title
     * @param string $message
     * @return Notification|false
     */
    public static function systemUpdate($userId, $title, $message)
    {
        return self::create(
            $userId,
            $title,
            $message,
            'system_update',
            null,
            []
        );
    }

    /**
     * Send security alert notification
     *
     * @param string $userId
     * @param string $message
     * @return Notification|false
     */
    public static function securityAlert($userId, $message)
    {
        return self::create(
            $userId,
            'Security Alert',
            $message,
            'security_alert',
            '/portal/profile',
            []
        );
    }

    /**
     * Send evaluation failed notification
     *
     * @param string $userId
     * @param string $evaluationName
     * @param string $reason
     * @return Notification|false
     */
    public static function evaluationFailed($userId, $evaluationName, $reason = '')
    {
        $message = "Your '{$evaluationName}' evaluation failed to complete.";
        if ($reason) {
            $message .= " Reason: {$reason}";
        }

        return self::create(
            $userId,
            'Evaluation Failed',
            $message,
            'evaluation_failed',
            '/portal/risk-evaluation',
            ['reason' => $reason]
        );
    }

    /**
     * Send welcome notification to new users
     *
     * @param string $userId
     * @param string $userName
     * @return Notification|false
     */
    public static function welcomeUser($userId, $userName)
    {
        return self::create(
            $userId,
            'Welcome to RaiDOT!',
            "Hi {$userName}! Welcome to RaiDOT. Start exploring our AI risk evaluation and awareness tools.",
            'welcome',
            '/portal/dashboard',
            []
        );
    }

    /**
     * Send bulk notifications to multiple users
     *
     * @param array $userIds
     * @param string $title
     * @param string $message
     * @param string $type
     * @param string|null $actionUrl
     * @param array $data
     * @return bool
     */
    public static function sendBulk($userIds, $title, $message, $type = 'system_update', $actionUrl = null, $data = [])
    {
        if (empty($userIds) || !is_array($userIds)) {
            Log::warning('Attempted to send bulk notifications with empty or invalid user IDs');
            return false;
        }

        // Validate that users exist
        $validUserIds = User::whereIn('_id', $userIds)->pluck('_id')->toArray();
        if (empty($validUserIds)) {
            Log::warning('No valid users found for bulk notification');
            return false;
        }

        $notifications = [];
        $now = Carbon::now();

        foreach ($validUserIds as $userId) {
            $notifications[] = [
                'user_id' => $userId,
                'title' => trim($title),
                'message' => trim($message),
                'type' => $type,
                'action_url' => $actionUrl,
                'data' => is_array($data) ? $data : [],
                'read_at' => null,
                'created_at' => $now,
                'updated_at' => $now
            ];
        }

        try {
            $result = Notification::insert($notifications);
            Log::info("Bulk notifications sent successfully", [
                'count' => count($notifications),
                'type' => $type
            ]);
            return $result;
        } catch (\Exception $e) {
            Log::error('Failed to send bulk notifications: ' . $e->getMessage(), [
                'user_count' => count($userIds),
                'type' => $type,
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Get notification count for user
     *
     * @param string $userId
     * @return int
     */
    public static function getUnreadCount($userId)
    {
        try {
            return Notification::where('user_id', $userId)
                ->whereNull('read_at')
                ->count();
        } catch (\Exception $e) {
            Log::error('Failed to get unread count: ' . $e->getMessage(), [
                'user_id' => $userId
            ]);
            return 0;
        }
    }

    /**
     * Mark notifications as read by type
     *
     * @param string $userId
     * @param string $type
     * @return bool
     */
    public static function markTypeAsRead($userId, $type)
    {
        try {
            $count = Notification::where('user_id', $userId)
                ->where('type', $type)
                ->whereNull('read_at')
                ->update(['read_at' => Carbon::now()]);
            
            Log::info("Marked {$count} notifications as read", [
                'user_id' => $userId,
                'type' => $type
            ]);
            
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to mark type as read: ' . $e->getMessage(), [
                'user_id' => $userId,
                'type' => $type
            ]);
            return false;
        }
    }

    /**
     * Mark a single notification as read
     *
     * @param string $notificationId
     * @param string $userId
     * @return bool
     */
    public static function markAsRead($notificationId, $userId)
    {
        try {
            $notification = Notification::where('_id', $notificationId)
                ->where('user_id', $userId)
                ->first();

            if (!$notification) {
                Log::warning("Notification not found or doesn't belong to user", [
                    'notification_id' => $notificationId,
                    'user_id' => $userId
                ]);
                return false;
            }

            $notification->read_at = Carbon::now();
            return $notification->save();
        } catch (\Exception $e) {
            Log::error('Failed to mark notification as read: ' . $e->getMessage(), [
                'notification_id' => $notificationId,
                'user_id' => $userId
            ]);
            return false;
        }
    }

    /**
     * Mark all notifications as read for a user
     *
     * @param string $userId
     * @return bool
     */
    public static function markAllAsRead($userId)
    {
        try {
            $count = Notification::where('user_id', $userId)
                ->whereNull('read_at')
                ->update(['read_at' => Carbon::now()]);

            Log::info("Marked all {$count} notifications as read for user", [
                'user_id' => $userId
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to mark all notifications as read: ' . $e->getMessage(), [
                'user_id' => $userId
            ]);
            return false;
        }
    }

    /**
     * Delete old notifications (cleanup)
     *
     * @param int $daysOld
     * @return bool
     */
    public static function deleteOldNotifications($daysOld = 30)
    {
        try {
            $cutoffDate = Carbon::now()->subDays($daysOld);
            $count = Notification::where('created_at', '<', $cutoffDate)->delete();
            
            Log::info("Deleted {$count} old notifications", [
                'days_old' => $daysOld,
                'cutoff_date' => $cutoffDate->toDateString()
            ]);
            
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to delete old notifications: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get notifications for a user with pagination
     *
     * @param string $userId
     * @param int $limit
     * @param int $offset
     * @param string|null $type
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getUserNotifications($userId, $limit = 20, $offset = 0, $type = null)
    {
        try {
            $query = Notification::where('user_id', $userId);
            
            if ($type) {
                $query->where('type', $type);
            }
            
            return $query->orderBy('created_at', 'desc')
                ->skip($offset)
                ->limit($limit)
                ->get();
        } catch (\Exception $e) {
            Log::error('Failed to get user notifications: ' . $e->getMessage(), [
                'user_id' => $userId,
                'type' => $type
            ]);
            return collect();
        }
    }

    /**
     * Check if user has unread notifications of a specific type
     *
     * @param string $userId
     * @param string $type
     * @return bool
     */
    public static function hasUnreadOfType($userId, $type)
    {
        try {
            return Notification::where('user_id', $userId)
                ->where('type', $type)
                ->whereNull('read_at')
                ->exists();
        } catch (\Exception $e) {
            Log::error('Failed to check unread notifications of type: ' . $e->getMessage(), [
                'user_id' => $userId,
                'type' => $type
            ]);
            return false;
        }
    }

    /**
     * Delete a notification
     *
     * @param string $notificationId
     * @param string $userId
     * @return bool
     */
    public static function deleteNotification($notificationId, $userId)
    {
        try {
            $deleted = Notification::where('_id', $notificationId)
                ->where('user_id', $userId)
                ->delete();

            if ($deleted) {
                Log::info("Notification deleted successfully", [
                    'notification_id' => $notificationId,
                    'user_id' => $userId
                ]);
                return true;
            }

            Log::warning("Notification not found or doesn't belong to user", [
                'notification_id' => $notificationId,
                'user_id' => $userId
            ]);
            return false;
        } catch (\Exception $e) {
            Log::error('Failed to delete notification: ' . $e->getMessage(), [
                'notification_id' => $notificationId,
                'user_id' => $userId
            ]);
            return false;
        }
    }
}