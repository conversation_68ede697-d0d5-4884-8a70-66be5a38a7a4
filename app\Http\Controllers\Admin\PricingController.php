<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Pricing;
use Illuminate\Support\Facades\Log;
use App\Services\StripeService;

class PricingController extends Controller
{
    protected $stripeService;
    public function __construct(StripeService $stripeService)
    {
        $this->stripeService = $stripeService;
    }
    private function success(string $msg, $data = null)
    {
        return response()->json([ 'status' => 200, 'msg' => $msg, 'data' => $data], 200);
    }
    private function error(string $msg, int $code = 400)
    {
        return response()->json([ 'status' => $code, 'msg' => $msg], $code);
    }
    private function pricingRules(): array
    {
        return [
            'package_name' => 'required|string',
            'package_level' => 'required|numeric',
            'package_price' => 'required|numeric',
            'currency' => 'required|string',
            'risk_evaluation' => 'nullable|array',
            'fair_decision_analysis' => 'nullable|array',
            'training' => 'required|boolean',
            'online_consultancy_hours' => 'nullable|numeric',
            'in_person_services' => 'nullable|array',
        ];
    }
    public function createPricing(Request $request)
    {
        $validated_price_package = $request->validate($this->pricingRules());
        $pricings = Pricing::all();
        foreach ($pricings as $pricing) {
            if (strtolower($pricing->package_name) == strtolower($validated_price_package['package_name'])) {
                return $this->error('Pricing with this package name already exists!', 400);
            }
        }
        $validated_price_package['stripe_product_id'] = null;
        $validated_price_package['stripe_price_id'] = null;
        try{
            if ($validated_price_package['package_price'] > 0) {
                $stripeData = $this->stripeService->createStripeProductAndPrice($validated_price_package);
                $validated_price_package['stripe_product_id'] = $stripeData['stripe_product_id'];
                $validated_price_package['stripe_price_id'] = $stripeData['stripe_price_id'];
            }
            $pricing = Pricing::create($validated_price_package);
            return $this->success('Pricing created successfully!', $pricing);
        } catch (\Exception $e) {
            return $this->error('Stripe error: ' . $e->getMessage(), 500);
        }
    }
    public function getAllPricing()
    {
        Log::info('Pricings fetched successfully!');
        $pricings = Pricing::all();
        return $this->success('Pricings fetched successfully!', $pricings);
    }
    public function getSinglePricing($id)
    {
        $pricing = Pricing::find($id);

        if (!$pricing) return $this->error('Pricing not found.', 404);

        return $this->success('Pricing fetched successfully!', $pricing);
    }
    public function deletePricing($id)
    {
        $pricing = Pricing::find($id);

        if (!$pricing) return $this->error('Pricing not found.', 404);

        $this->stripeService->archiveProductIfExists($pricing->stripe_product_id);

        $pricing->delete();

        return $this->success('Pricing deleted successfully.');
    }
    public function updatePricing(Request $request, $id)
    {
        $validated_price_package = $request->validate($this->pricingRules());

        $pricing = Pricing::find($id);

        if (!$pricing) return $this->error('Pricing not found.', 404);
        $updated_stripe_details = $this->stripeService->updateProductAndPrice($validated_price_package, $pricing->stripe_product_id);
        $validated_price_package = array_merge($validated_price_package, $updated_stripe_details);
        $pricing->update($validated_price_package);

        return $this->success('Pricing updated successfully!', $pricing);
    }
}