<?php

namespace App\Services\Admin\AwarenessManagement;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class LessonValidator
{
    protected $rules = [
        'title' => 'required|string',
        'description' => 'nullable|string',
        'lesson_type' => 'required|string|in:content,quiz,final_exam',
        'order' => 'sometimes|integer',
    ];

    protected $updateRules = [
        'title' => 'sometimes|required|string',
        'description' => 'sometimes|nullable|string',
        'lesson_type' => 'sometimes|required|string|in:content,quiz,final_exam',
        'order' => 'sometimes|integer',
    ];

    public function validate(Request $request, bool $isCreate = true): array
    {
        $rules = $isCreate ? $this->rules : $this->updateRules;
        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return $request->only(array_keys($rules));
    }
}