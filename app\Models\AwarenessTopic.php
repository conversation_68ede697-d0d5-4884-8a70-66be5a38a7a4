<?php

namespace App\Models;

use <PERSON><PERSON><PERSON>\Mongodb\Eloquent\Model as Eloquent;

class AwarenessTopic extends Eloquent
{
    protected $connection = 'mongodb';
    protected $collection = 'awareness_topics';
    protected $fillable = [
        'awareness_id',
        'order',
        'title'
    ];
    protected $hidden = [
        'updated_at',
        'created_at'
    ];
    public $timestamps = true;

    public function awareness()
    {
        return $this->belongsTo(Awareness::class, 'awareness_id');
    }

    public function lessons()
    {
        return $this->hasMany(AwarenessTopicLesson::class, 'topic_id');
    }
}