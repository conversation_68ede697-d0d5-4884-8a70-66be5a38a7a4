<?php

namespace App\Repositories\Admin\AwarenessManagement;

use App\Interfaces\Admin\AwarenessManagement\AwarenessTopicLessonQuestionRepositoryInterface;
use Illuminate\Http\Request;
use App\Services\Admin\AwarenessManagement\QuestionValidator;
use App\Repositories\Admin\AwarenessManagement\Traits\AwarenessHierarchyTrait;

class AwarenessTopicLessonQuestionRepository implements AwarenessTopicLessonQuestionRepositoryInterface
{
    use AwarenessHierarchyTrait;

    protected $questionValidator;

    public function __construct(QuestionValidator $questionValidator)
    {
        $this->questionValidator = $questionValidator;
    }

    public function createOrUpdate(Request $request, string $awarenessId, string $topicId, string $lessonId): array
    {
        try {
            $lesson = $this->getLesson($awarenessId, $topicId, $lessonId);
            $data = $this->questionValidator->validate($request);
            $data['order'] = $request->has('order') ? $request->order : ($lesson->questions()->max('order') ?? 0) + 1;

            $question = null;
            $message = '';

            if (!empty($request->_id)) {
                $question = $lesson->questions()->find($request->_id);
                if (!$question) return ['status' => 404, 'message' => 'Question not found'];
                
                $question->update($data);
                $message = 'Question updated successfully';
            } else {
                $question= $lesson->questions()->create($data);
                $message = 'Question created successfully';
            }

            return [ 'status' => 200, 'data' => $question->fresh(), 'message' => $message];
            
        } catch (\Exception $e) {
            return ['status' => 500, 'error' => $e->getMessage()];
        }
    }

    public function delete(Request $request, string $awarenessId, string $topicId, string $lessonId, string $questionId): array
    {
        try {
            $lesson = $this->getLesson($awarenessId, $topicId, $lessonId);

            $question = $lesson->questions()->find($questionId);
            if (!$question) return ['status' => 404, 'message' => 'Question not found'];
            
            $question->delete();

            return ['status' => 200, 'message' => 'Question deleted successfully'];
        } catch (\Exception $e) {
            return ['status' => 500, 'error' => $e->getMessage()];
        }
    }
}
