<?php

namespace App\Repositories\Admin\AwarenessManagement;

use Illuminate\Http\Request;
use Illuminate\Support\Str;
use App\Models\Awareness;
use App\Interfaces\Admin\AwarenessManagement\AwarenessRepositoryInterface;
use App\Repositories\Admin\AwarenessManagement\Traits\AwarenessHierarchyTrait;
use App\Services\Admin\AwarenessManagement\AwarenessValidator;


class AwarenessRepository implements AwarenessRepositoryInterface
{
    use AwarenessHierarchyTrait;
    protected $validator;
    public function __construct(AwarenessValidator $validator)
    {
        $this->validator = $validator;
    }
    public function list(Request $request): array
    {
        try {
            $query = $this->buildListQuery($request);
            $courses = $query->orderBy('_id', 'desc')->get();
            return ['status' => 200, 'data' => $courses];
        } catch (\Exception $e) {
            return ['status' => 500, 'message' => $e->getMessage()];
        }
    }
    public function create(Request $request): array
    {
        try {
            $validatedData = $this->validator->validate($request, true);
            $validatedData['slug'] = Str::slug($validatedData['title']);
            $validatedData['package_id'] = $validatedData['selected_package']['_id'];
            $validatedData['package_name'] = $validatedData['selected_package']['package_name'];
            unset($validatedData['selected_package']);
            
            Awareness::create($validatedData);
            
            return ['status' => 200, 'message' => 'Awareness created successfully'];
        } catch (\Exception $e) {
            return ['status' => 500, 'message' => $e->getMessage()];
        }
    }
    public function single(Request $request, string $id): array
    {
        try {
            $awareness = $this->getAwareness($id);
            return ['status' => 200, 'data' => $awareness];
        } catch (\Exception $e) {
            return ['status' => 500, 'error' => $e->getMessage()];
        }
    }
    public function update(Request $request, string $id): array
    {
        try {
            $awareness = $this->getAwareness($id);
            $validatedData = $this->validator->validate($request, false);

            if (isset($validatedData['title'])) {
                $validatedData['slug'] = Str::slug($validatedData['title']);
            }
            if (isset($validatedData['selected_package'])) {
                $validatedData['package_id'] = $validatedData['selected_package']['_id'];
                $validatedData['package_name'] = $validatedData['selected_package']['package_name'];
                unset($validatedData['selected_package']);
            }
            $awareness->update($validatedData);
            return ['status' => 200, 'message' => 'Awareness updated successfully'];
        } catch (\Exception $e) {
            return ['status' => 500, 'error' => $e->getMessage()];
        }
    }
    public function delete(Request $request, string $id): array
    {
        try {
            $awareness = $this->getAwareness($id);
            $this->cascadeDeleteAwareness($awareness);
            return ['status' => 200, 'message' => 'Awareness course deleted successfully.'];
        } catch (\Exception $e) {
            return ['status' => 500, 'error' => $e->getMessage()];
        }
    }
    private function cascadeDeleteAwareness(Awareness $awareness): void
    {
        $topics = $awareness->topics()->get();
        foreach ($topics as $topic) {
            $lessons = $topic->lessons()->get();
            foreach ($lessons as $lesson) {
                $lesson->questions()->delete();
            }
            $topic->lessons()->delete();
        }
        $awareness->topics()->delete();
        $awareness->delete();
    }
    protected function buildListQuery(Request $request)
    {
        $query = Awareness::select('*');
        if (!empty($request->keyword)) {
            $query->where('title', 'like', '%' . $request->keyword . '%');
        }
        return $query;
    }
}