<?php

namespace App\Repositories\Admin\AwarenessManagement\Traits;

use App\Models\Awareness;

trait AwarenessHierarchyTrait
{
    protected function getAwareness(string $awarenessId)
    {
        return Awareness::findOrFail($awarenessId);
    }

    protected function getTopic(string $awarenessId, string $topicId)
    {
        $awareness = $this->getAwareness($awarenessId);
        return $awareness->topics()->findOrFail($topicId);
    }
 
    protected function getLesson(string $awarenessId, string $topicId, string $lessonId)
    {
        $topic = $this->getTopic($awarenessId, $topicId);
        return $topic->lessons()->findOrFail($lessonId);
    }
}