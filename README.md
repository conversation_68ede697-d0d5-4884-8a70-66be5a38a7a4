# RaiDOT

If we want to backup mongodb database

1. extract the mongo database in a folder (eg: F:\D-Ready\Raidot\mot4ai)
2. Download the mongodb database tools (from https://www.mongodb.com/try/download/database-tools)
3. Extract the .zip to a directory (e.g., C:\mongodb-database-tools)
4. Add the bin folder to your system PATH (C:\mongodb-database-tools\bin)
5. Open terminal and do this 'mongorestore --db mot4ai F:\D-Ready\Raidot\mot4ai'
6. To make a backup of your database: 'mongodump --db=mot4ai --out=F:\D-Ready\Raidot'
