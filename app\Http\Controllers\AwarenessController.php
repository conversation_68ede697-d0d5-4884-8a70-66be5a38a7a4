<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Repositories\Front\AwarenessRepository;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class AwarenessController extends Controller
{
    public function course_certificate(Request $request, $id)
    {
        return AwarenessRepository::course_certificate($request, $id);
    }

    public function topic_list(Request $request, $awareness_id): JsonResponse
    {
        $rv = AwarenessRepository::topic_list($request, $awareness_id);
        return response()->json($rv, 200);
    }

    public function topic_lesson_single(Request $request, $awareness_id, $topic_id, $id): JsonResponse
    {
        $rv = AwarenessRepository::topic_lesson_single($request, $awareness_id, $topic_id, $id);
        return response()->json($rv, 200);
    }

    public function topic_lesson_single_complete(Request $request, $awareness_id, $topic_id, $id): JsonResponse
    {
        $rv = AwarenessRepository::topic_lesson_single_complete($request, $awareness_id, $topic_id, $id);
        return response()->json($rv, 200);
    }

    public function topic_lesson_single_quiz_submit(Request $request, $awareness_id, $topic_id, $id): JsonResponse
    {
        $rv = AwarenessRepository::topic_lesson_single_quiz_submit($request, $awareness_id, $topic_id, $id);
        return response()->json($rv, 200);
    }

    public function aware_evaluations(Request $request): JsonResponse
    {
        $rv = AwarenessRepository::aware_evaluations($request);
        return response()->json($rv, 200);
    }

    public function getCourses(Request $request): JsonResponse
    {
        $rv = AwarenessRepository::getCourses($request);
        return response()->json($rv, 200);
    }

    public function getSingleCourse(Request $request, $id): JsonResponse
    {
        $rv = AwarenessRepository::getSingleCourse($request, $id);
        return response()->json($rv, 200);
    }
}
