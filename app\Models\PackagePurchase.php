<?php

namespace App\Models;

use Jenssegers\Mongodb\Eloquent\Model;

class PackagePurchase  extends Model
{
    protected $connection = 'mongodb';
    protected $collection = 'package_purchases';

    protected $fillable = [
        'pricing_package_id',
        'user_id',
        'status',
        'status_cause',
        'payment_details',
        'stripe_customer',
    ];

    protected $casts = [
        'status' => 'string',
        'status_cause' => 'string',
        'payment_details' => 'array',
        'stripe_customer' => 'array',
    ];

    protected $attributes = [
        'status_cause' => 'default',
    ];
    public function package()
    {
        return $this->belongsTo(Pricing::class, 'pricing_package_id', '_id');
    }
}