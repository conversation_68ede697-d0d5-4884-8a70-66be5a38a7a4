<?php

use App\Http\Controllers\Admin\Api\AuthController;
use App\Http\Controllers\Admin\Api\CertificateController;
use App\Http\Controllers\Admin\Api\EvaluationSectorsController;
use App\Http\Controllers\Admin\Api\MediaController;
use App\Http\Controllers\Admin\Api\QuestionController;
use App\Http\Controllers\Admin\Api\ReportController;
use App\Http\Controllers\Admin\Api\RiskFactorController;
use App\Http\Controllers\Admin\Api\WorkshopController;
use App\Http\Controllers\Admin\Api\UserController;
use App\Http\Controllers\Admin\PricingController;
use App\Http\Controllers\Admin\AwarenessManagement\AwarenessController;
use App\Http\Controllers\Admin\AwarenessManagement\AwarenessTopicController;
use App\Http\Controllers\Admin\AwarenessManagement\AwarenessTopicLessonController;
use App\Http\Controllers\Admin\AwarenessManagement\AwarenessTopicLessonQuestionController;
use App\Http\Controllers\Admin\ConsultancyController;

use Illuminate\Support\Facades\Route;

// Group of routes related to authentication
Route::group(['prefix' => 'auth'], function () {
    // Authentication routes
    Route::post('login', [AuthController::class, 'Login'])->middleware('AdminAuthCheck');
    Route::get('logout', [AuthController::class, 'Logout'])->middleware('AdminAuthReqCheck');
    Route::post('forgot/request', [AuthController::class, 'Forgot'])->middleware('AdminAuthCheck');
    Route::post('reset/password', [AuthController::class, 'Reset'])->middleware('AdminAuthCheck');
});
Route::get('/token-refresh', [AuthController::class, 'Refresh'])->name('api.admin.token.refresh');

// Routes that require authentication
Route::middleware('AdminAuthReqCheck')->group(function () {
    // General routes
    Route::prefix('general')->group(function () {
        Route::post('me', [AuthController::class, 'GetProfile']);
        Route::post('profile/update', [AuthController::class, 'UpdateProfile']);
        Route::post('change/password', [AuthController::class, 'ChangePassword']);
    });

    // Dashboard and report routes
    Route::prefix('dashboard/report')->group(function () {
        Route::post('evaluations', [ReportController::class, 'evaluation_report']);
        Route::post('fair-decision', [ReportController::class, 'fair_decision_report']);
        Route::post('registered-users', [ReportController::class, 'registered_users_report']);
    });

    // Evaluation routes
    Route::prefix('evaluation')->group(function () {
        // Risk Factors routes
        Route::prefix('risk-factor')->group(function () {
            Route::post('get/all', [RiskFactorController::class, 'GetAll']);
            Route::post('manage', [RiskFactorController::class, 'Manage']);
        });

        // Evaluation Sectors routes
        Route::prefix('sector')->group(function () {
            Route::post('get/all', [EvaluationSectorsController::class, 'GetAll']);
            Route::post('manage', [EvaluationSectorsController::class, 'Manage']);
        });

        // Fair Decision Sectors routes
        Route::prefix('fd/sector')->group(function () {
            Route::post('get/all', [EvaluationSectorsController::class, 'GetAllFdSectors']);
            Route::post('manage', [EvaluationSectorsController::class, 'ManageFdSectors']);
        });

        // Question Groups routes
        Route::prefix('question/groups')->group(function () {
            Route::post('get/all', [EvaluationSectorsController::class, 'GetAllQuestionGroups']);
            Route::post('manage', [EvaluationSectorsController::class, 'ManageQuestionGroups']);
        });

        // Awareness Evaluation routes
        Route::prefix('awareness')->group(function () {
            Route::post('list', [AwarenessController::class, 'list']);
            Route::post('create', [AwarenessController::class, 'create']);
            Route::get('single/{id}', [AwarenessController::class, 'single']);
            Route::post('update/{id}', [AwarenessController::class, 'update']);
            Route::delete('delete/{id}', [AwarenessController::class, 'delete']);
            
            Route::prefix('{awareness_id}/topic')->group(function () {
                Route::get('list', [AwarenessTopicController::class, 'list']);
                Route::post('create', [AwarenessTopicController::class, 'create']);
                Route::post('update/{id}', [AwarenessTopicController::class, 'update']);
                Route::delete('delete/{id}', [AwarenessTopicController::class, 'delete']);
                
                Route::prefix('{topic_id}/lesson')->group(function () {
                    Route::post('create', [AwarenessTopicLessonController::class, 'create']);
                    Route::get('single/{id}', [AwarenessTopicLessonController::class, 'single']);
                    Route::post('update/{id}', [AwarenessTopicLessonController::class, 'update']);
                    Route::delete('delete/{id}', [AwarenessTopicLessonController::class, 'delete']);
                    
                    Route::prefix('{lesson_id}/question')->group(function () {
                        Route::post('create-or-update', [AwarenessTopicLessonQuestionController::class, 'createOrUpdate']);
                        Route::delete('delete/{id}', [AwarenessTopicLessonQuestionController::class, 'delete']);
                    });
                });
            });
        });
    });

    // Question routes
    Route::prefix('question')->group(function () {
        Route::get('types', [QuestionController::class, 'GetQuestionTypes']);
        Route::post('list', [QuestionController::class, 'List']);
        Route::post('manage', [QuestionController::class, 'Manage']);
        Route::post('delete', [QuestionController::class, 'Delete']);
    });

    // User routes
    Route::prefix('user')->group(function () {
        Route::post('list', [UserController::class, 'list']);
        Route::post('create', [UserController::class, 'create']);
        Route::post('single', [UserController::class, 'single']);
        Route::post('update', [UserController::class, 'update']);
        Route::post('delete', [UserController::class, 'delete']);
    });

    // Workshop routes
    Route::prefix('workshop')->group(function () {
        Route::post('list', [WorkshopController::class, 'list']);
        Route::post('create', [WorkshopController::class, 'create']);
        Route::post('single', [WorkshopController::class, 'single']);
        Route::post('certificates', [WorkshopController::class, 'certificates']);
        Route::post('update', [WorkshopController::class, 'update']);
        Route::post('delete', [WorkshopController::class, 'delete']);
    });

    // Evaluation Certificate routes
    Route::prefix('evaluation/certificate')->group(function () {
        Route::post('settings', [CertificateController::class, 'evaluation_certificate_settings']);
        Route::post('settings/update', [CertificateController::class, 'evaluation_certificate_settings_update']);
        Route::post('list', [CertificateController::class, 'evaluation_certificates']);
    });

    // Participant Certificate routes
    Route::prefix('participant/certificate')->group(function () {
        Route::post('list', [CertificateController::class, 'participant_certificates']);
    });

    Route::prefix('awareness/certificate')->group(function () {
        Route::post('list', [CertificateController::class, 'awareness_certificates']);
    });

    // Media routes
    Route::prefix('media')->group(function () {
        Route::post('upload', [MediaController::class, 'Upload']);
    });

    // Pricing routes
    Route::prefix('pricing')->group(function () {
        Route::post('/create', [PricingController::class, 'createPricing']);
        Route::get('/list', [PricingController::class, 'getAllPricing']);
        Route::get('/single/{id}', [PricingController::class, 'getSinglePricing']);
        Route::put('/update/{id}', [PricingController::class, 'updatePricing']);
        Route::delete('/delete/{id}', [PricingController::class, 'deletePricing']);
    });
    Route::prefix('consultancy')->group(function () {
    // Slot Management - FIXED METHODS
    Route::get('slots', [ConsultancyController::class, 'getSlots']);         // Changed from POST to GET
    Route::post('slots/create', [ConsultancyController::class, 'createSlot']);
    Route::post('slots/update', [ConsultancyController::class, 'updateSlot']);
    Route::delete('slots/delete/{id}', [ConsultancyController::class, 'deleteSlot']);
    Route::get('slots/{id}', [ConsultancyController::class, 'getSlot']);
    
    // Booking Management - FIXED METHODS  
    Route::get('bookings', [ConsultancyController::class, 'getBookings']);    // Changed from POST to GET
    Route::get('bookings/{id}', [ConsultancyController::class, 'getBooking']);
    Route::post('bookings/update', [ConsultancyController::class, 'updateBooking']);
    Route::post('bookings/cancel', [ConsultancyController::class, 'cancelBooking']);
    Route::post('bookings/confirm', [ConsultancyController::class, 'confirmBooking']);
    
    // Meeting Link Management
    Route::post('bookings/meeting-link', [ConsultancyController::class, 'updateMeetingLink']);
    Route::post('bookings/send-link', [ConsultancyController::class, 'sendMeetingLink']);
    
    // Statistics and Reports
    Route::get('stats', [ConsultancyController::class, 'getStats']);
    Route::post('reports/bookings', [ConsultancyController::class, 'getBookingReport']);
    Route::post('reports/usage', [ConsultancyController::class, 'getUsageReport']);
    
    // User Consultancy Management
    Route::post('users/usage', [ConsultancyController::class, 'getUserUsage']);
    Route::post('users/usage/update', [ConsultancyController::class, 'updateUserUsage']);
    Route::post('users/initialize', [ConsultancyController::class, 'initializeUserConsultancy']);
    
    // Location Management
    Route::get('locations/countries', [ConsultancyController::class, 'getCountries']);
    Route::get('locations/cities/{country}', [ConsultancyController::class, 'getCities']);

    });
});