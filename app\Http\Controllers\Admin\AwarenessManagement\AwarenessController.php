<?php

namespace App\Http\Controllers\Admin\AwarenessManagement;

use App\Http\Controllers\Controller;
use App\Interfaces\Admin\AwarenessManagement\AwarenessRepositoryInterface;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class AwarenessController extends Controller
{
    private $awarenessRepository;

    public function __construct(AwarenessRepositoryInterface $awarenessRepository)
    {
        $this->awarenessRepository = $awarenessRepository;
    }
    public function list(Request $request): JsonResponse
    {
        $response = $this->awarenessRepository->list($request);
        return response()->json($response, $response['status']);
    }
    public function create(Request $request): JsonResponse
    {
        $response = $this->awarenessRepository->create($request);
        return response()->json($response, $response['status']);
    }
    public function single(Request $request, $id): JsonResponse
    {
        $response = $this->awarenessRepository->single($request, $id);
        return response()->json($response, $response['status']);
    }
    public function update(Request $request, $id): JsonResponse
    {
        $response = $this->awarenessRepository->update($request, $id);
        return response()->json($response, $response['status']);
    }
    public function delete(Request $request, $id): JsonResponse
    {
        $response = $this->awarenessRepository->delete($request, $id);
        return response()->json($response, $response['status']);
    }
}
