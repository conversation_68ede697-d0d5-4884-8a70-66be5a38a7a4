<?php
// FIXED app/Repositories/ConsultancyRepository.php

namespace App\Repositories;

use App\Models\ConsultancySlot;
use App\Models\ConsultancyBooking;
use App\Models\ConsultancyUsage;
use App\Models\User;
use App\Models\Pricing;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class ConsultancyRepository
{
    /**
     * Get all consultancy slots with pagination and filters - FIXED
     */
public static function getSlots($request)
{
    try {
        $query = \App\Models\ConsultancySlot::query();

        // Apply filters without relationships to avoid date casting issues
        if (isset($request->type) && !empty($request->type)) {
            $query->where('type', $request->type);
        }

        if (isset($request->date) && !empty($request->date)) {
            $query->where('date', 'like', '%' . $request->date . '%');
        }

        if (isset($request->country) && !empty($request->country)) {
            $query->where('country', $request->country);
        }

        if (isset($request->is_active)) {
            $query->where('is_active', $request->is_active);
        }

        $perPage = $request->per_page ?? 15;
        
        // Get raw data without eager loading to avoid date casting issues
        $slots = $query->orderBy('created_at', 'desc')->paginate($perPage);

        // Convert to simple array structure to avoid model casting issues
        $slotsArray = [];
        foreach ($slots->items() as $slot) {
            $slotArray = [
                '_id' => $slot->_id,
                'date' => is_array($slot->getRawOriginal('date')) ? 
                    ($slot->getRawOriginal('date')['date'] ?? date('Y-m-d')) : 
                    $slot->getRawOriginal('date'),
                'start_time' => is_array($slot->getRawOriginal('start_time')) ? 
                    ($slot->getRawOriginal('start_time')['date'] ?? date('Y-m-d H:i:s')) : 
                    $slot->getRawOriginal('start_time'),
                'end_time' => is_array($slot->getRawOriginal('end_time')) ? 
                    ($slot->getRawOriginal('end_time')['date'] ?? date('Y-m-d H:i:s')) : 
                    $slot->getRawOriginal('end_time'),
                'type' => $slot->type,
                'location' => $slot->location,
                'country' => $slot->country,
                'city' => $slot->city,
                'address' => $slot->address,
                'max_capacity' => $slot->max_capacity ?? 1,
                'is_active' => $slot->is_active ?? true,
                'notes' => $slot->notes,
                'duration_minutes' => $slot->duration_minutes ?? 60,
                'meeting_link_template' => $slot->meeting_link_template,
                'created_by_admin' => $slot->created_by_admin,
                // Add computed fields manually
                'confirmed_bookings' => 0, // We'll get this separately if needed
                'available_capacity' => $slot->max_capacity ?? 1,
                'is_full' => false,
            ];
            $slotsArray[] = $slotArray;
        }

        // Reconstruct pagination data
        $paginationData = [
            'current_page' => $slots->currentPage(),
            'data' => $slotsArray,
            'first_page_url' => $slots->url(1),
            'from' => $slots->firstItem(),
            'last_page' => $slots->lastPage(),
            'last_page_url' => $slots->url($slots->lastPage()),
            'links' => [],
            'next_page_url' => $slots->nextPageUrl(),
            'path' => $slots->path(),
            'per_page' => $slots->perPage(),
            'prev_page_url' => $slots->previousPageUrl(),
            'to' => $slots->lastItem(),
            'total' => $slots->total(),
        ];

        return ['status' => 200, 'data' => $paginationData];
    } catch (\Exception $e) {
        \Log::error('Error in getSlots: ' . $e->getMessage());
        return ['status' => 500, 'error' => $e->getMessage()];
    }
}
    /**
     * Get single slot - ADDED MISSING METHOD
     */
    public static function getSingleSlot($id)
    {
        try {
            $slot = ConsultancySlot::with('bookings.user')->findOrFail($id);
            
            // Add computed fields
            $slot->available_capacity = $slot->available_capacity;
            $slot->is_full = $slot->is_full;
            $slot->confirmed_bookings = $slot->bookings->where('status', 'confirmed')->count();
            
            return ['status' => 200, 'data' => $slot];
        } catch (\Exception $e) {
            return ['status' => 500, 'error' => $e->getMessage()];
        }
    }

    /**
     * Create or update consultancy slot - FIXED
     */
public static function manageSlot($request)
{
    try {
        $data = $request->all();
        
        // SIMPLIFIED: Let the model handle date conversion
        // Just ensure we have the basic required fields
        if (isset($data['date']) && isset($data['start_time']) && isset($data['end_time'])) {
            // Store as simple strings, let the model process them
            $data['date'] = $data['date'];
            $data['start_time'] = $data['start_time'];
            $data['end_time'] = $data['end_time'];
            
            // Calculate duration in minutes
            try {
                $startDateTime = Carbon::parse($data['date'] . ' ' . $data['start_time']);
                $endDateTime = Carbon::parse($data['date'] . ' ' . $data['end_time']);
                $data['duration_minutes'] = $endDateTime->diffInMinutes($startDateTime);
            } catch (\Exception $e) {
                // If date parsing fails, let the model handle it
                \Log::warning('Date parsing warning in manageSlot: ' . $e->getMessage());
            }
        }

        if (isset($data['id'])) {
            // Update existing slot
            $slot = ConsultancySlot::findOrFail($data['id']);
            unset($data['id']); // Remove ID from update data
            $slot->update($data);
        } else {
            // Create new slot
            $data['created_by_admin'] = auth()->id();
            $slot = ConsultancySlot::create($data);
        }

        return ['status' => 200, 'data' => $slot, 'message' => 'Slot saved successfully'];
    } catch (\Exception $e) {
        return ['status' => 500, 'error' => $e->getMessage()];
    }
}
    /**
     * Delete a consultancy slot - ADDED MISSING METHOD
     */
    public static function deleteSlot($id)
    {
        try {
            $slot = ConsultancySlot::findOrFail($id);
            
            // Check if slot has any confirmed bookings
            $confirmedBookings = $slot->bookings()->where('status', 'confirmed')->count();
            if ($confirmedBookings > 0) {
                return ['status' => 400, 'error' => 'Cannot delete slot with confirmed bookings'];
            }
            
            // Cancel any pending bookings
            $slot->bookings()->where('status', 'pending')->update([
                'status' => 'cancelled',
                'cancellation_reason' => 'Slot deleted by administrator'
            ]);
            
            $slot->delete();
            
            return ['status' => 200, 'message' => 'Slot deleted successfully'];
        } catch (\Exception $e) {
            return ['status' => 500, 'error' => $e->getMessage()];
        }
    }

    /**
     * Get all bookings with pagination and filters - FIXED
     */
    public static function getBookings($request)
    {
        try {
            $query = ConsultancyBooking::with(['user', 'slot']);

            // Apply filters
            if (isset($request->status) && !empty($request->status)) {
                $query->where('status', $request->status);
            }

            if (isset($request->date_from) && !empty($request->date_from)) {
                $query->whereHas('slot', function($q) use ($request) {
                    $q->where('date', '>=', $request->date_from);
                });
            }

            if (isset($request->date_to) && !empty($request->date_to)) {
                $query->whereHas('slot', function($q) use ($request) {
                    $q->where('date', '<=', $request->date_to);
                });
            }

            if (isset($request->user_search) && !empty($request->user_search)) {
                $query->whereHas('user', function($q) use ($request) {
                    $q->where('name', 'like', '%' . $request->user_search . '%')
                      ->orWhere('email', 'like', '%' . $request->user_search . '%');
                });
            }

            $perPage = $request->per_page ?? 15;
            $bookings = $query->orderBy('created_at', 'desc')
                             ->paginate($perPage);

            return ['status' => 200, 'data' => $bookings];
        } catch (\Exception $e) {
            return ['status' => 500, 'error' => $e->getMessage()];
        }
    }

    /**
     * Get single booking - ADDED MISSING METHOD
     */
    public static function getBooking($id)
    {
        try {
            $booking = ConsultancyBooking::with(['user', 'slot'])->findOrFail($id);
            return ['status' => 200, 'data' => $booking];
        } catch (\Exception $e) {
            return ['status' => 500, 'error' => $e->getMessage()];
        }
    }

    /**
     * Update booking - ADDED MISSING METHOD
     */
    public static function updateBooking($request)
    {
        try {
            $booking = ConsultancyBooking::findOrFail($request->booking_id);
            
            $updateData = [];
            if ($request->has('status')) {
                $updateData['status'] = $request->status;
            }
            if ($request->has('meeting_link')) {
                $updateData['meeting_link'] = $request->meeting_link;
            }
            if ($request->has('notes')) {
                $updateData['notes'] = $request->notes;
            }
            if ($request->has('cancellation_reason')) {
                $updateData['cancellation_reason'] = $request->cancellation_reason;
            }
            
            $booking->update($updateData);
            
            return ['status' => 200, 'data' => $booking->load(['user', 'slot']), 'message' => 'Booking updated successfully'];
        } catch (\Exception $e) {
            return ['status' => 500, 'error' => $e->getMessage()];
        }
    }

    /**
     * Cancel booking - FIXED
     */
    public static function cancelBooking($request)
    {
        try {
            $booking = ConsultancyBooking::findOrFail($request->booking_id);
            
            if ($booking->status === 'cancelled') {
                return ['status' => 400, 'error' => 'Booking is already cancelled'];
            }
            
            // If booking was confirmed, refund the minutes to user
            if ($booking->status === 'confirmed') {
                $usage = ConsultancyUsage::where('user_id', $booking->user_id)->first();
                if ($usage) {
                    $usage->used_minutes = max(0, $usage->used_minutes - $booking->duration_minutes);
                    $usage->remaining_minutes += $booking->duration_minutes;
                    $usage->save();
                }
            }
            
            $booking->status = 'cancelled';
            $booking->cancellation_reason = $request->cancellation_reason ?? 'Cancelled by administrator';
            $booking->save();
            
            return ['status' => 200, 'data' => $booking->load(['user', 'slot']), 'message' => 'Booking cancelled successfully'];
        } catch (\Exception $e) {
            return ['status' => 500, 'error' => $e->getMessage()];
        }
    }

    /**
     * Book a consultancy session - FIXED
     */
    public static function bookSession($request)
    {
        try {
            $userId = $request->user_id ?? auth()->id();
            $slotId = $request->slot_id;
            
            // Get user and slot
            $user = User::findOrFail($userId);
            $slot = ConsultancySlot::findOrFail($slotId);
            
            // Check if slot is available
            if ($slot->is_full) {
                return ['status' => 400, 'error' => 'This time slot is fully booked'];
            }

            // Check user's consultancy usage
            $usage = ConsultancyUsage::where('user_id', $userId)->first();
            if (!$usage || !$usage->canBookDuration($slot->duration_minutes)) {
                return ['status' => 400, 'error' => 'Insufficient consultancy hours remaining'];
            }

            // Check if user already has a booking for this slot
            $existingBooking = ConsultancyBooking::where('user_id', $userId)
                                                ->where('slot_id', $slotId)
                                                ->whereIn('status', ['pending', 'confirmed'])
                                                ->first();
            
            if ($existingBooking) {
                return ['status' => 400, 'error' => 'You already have a booking for this time slot'];
            }

            // Create booking
            $booking = ConsultancyBooking::create([
                'user_id' => $userId,
                'slot_id' => $slotId,
                'duration_minutes' => $slot->duration_minutes,
                'status' => 'confirmed', // Auto-confirm for now
                'booking_reference' => ConsultancyBooking::generateBookingReference(),
                'notes' => $request->notes ?? '',
            ]);

            // Deduct minutes from user's usage
            $usage->deductMinutes($slot->duration_minutes);

            return ['status' => 200, 'data' => $booking->load(['user', 'slot']), 'message' => 'Session booked successfully'];
        } catch (\Exception $e) {
            return ['status' => 500, 'error' => $e->getMessage()];
        }
    }

    /**
     * Get consultancy statistics - FIXED
     */
    public static function getConsultancyStats()
    {
        try {
            $totalSlots = ConsultancySlot::count();
            $totalBookings = ConsultancyBooking::count();
            $activeSlots = ConsultancySlot::where('is_active', true)->count();
            $pendingBookings = ConsultancyBooking::where('status', 'pending')->count();
            $confirmedBookings = ConsultancyBooking::where('status', 'confirmed')->count();
            $cancelledBookings = ConsultancyBooking::where('status', 'cancelled')->count();
            
            $thisMonth = now()->startOfMonth();
            $monthlyBookings = ConsultancyBooking::where('created_at', '>=', $thisMonth)->count();
            
            $upcomingSessions = ConsultancyBooking::with('slot')
                ->where('status', 'confirmed')
                ->whereHas('slot', function($query) {
                    $query->where('date', '>=', now()->toDateString());
                })
                ->count();

            // Calculate today's bookings
            $todayBookings = ConsultancyBooking::with('slot')
                ->whereHas('slot', function($query) {
                    $query->whereDate('date', now()->toDateString());
                })
                ->count();

            // Get recent bookings
            $recentBookings = ConsultancyBooking::with(['user', 'slot'])
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get();

            return [
                'status' => 200,
                'data' => [
                    'total_slots' => $totalSlots,
                    'active_slots' => $activeSlots,
                    'total_bookings' => $totalBookings,
                    'pending_bookings' => $pendingBookings,
                    'confirmed_bookings' => $confirmedBookings,
                    'cancelled_bookings' => $cancelledBookings,
                    'monthly_bookings' => $monthlyBookings,
                    'upcoming_bookings' => $upcomingSessions,
                    'today_bookings' => $todayBookings,
                    'recent_bookings' => $recentBookings,
                    'booking_rate' => $totalSlots > 0 ? round(($totalBookings / $totalSlots) * 100, 2) : 0,
                ]
            ];
        } catch (\Exception $e) {
            return ['status' => 500, 'error' => $e->getMessage()];
        }
    }

    /**
     * Get available slots for user booking - FIXED
     */
    public static function getAvailableSlots($request)
    {
        try {
            $query = ConsultancySlot::with('bookings')
                ->where('is_active', true)
                ->where('date', '>=', now()->toDateString());

            // Apply filters
            if (isset($request->type) && !empty($request->type)) {
                $query->where('type', $request->type);
            }
            if (isset($request->date_from) && !empty($request->date_from)) {
                $query->where('date', '>=', $request->date_from);
            }
            if (isset($request->date_to) && !empty($request->date_to)) {
                $query->where('date', '<=', $request->date_to);
            }
            if (isset($request->country) && !empty($request->country)) {
                $query->where('country', $request->country);
            }

            $slots = $query->orderBy('date', 'asc')
                          ->orderBy('start_time', 'asc')
                          ->get();

            // Filter out full slots and add computed properties
            $availableSlots = $slots->filter(function ($slot) {
                return !$slot->is_full;
            })->map(function ($slot) {
                $slot->available_capacity = $slot->available_capacity;
                $slot->formatted_date = Carbon::parse($slot->date)->format('Y-m-d');
                $slot->formatted_start_time = Carbon::parse($slot->start_time)->format('H:i');
                $slot->formatted_end_time = Carbon::parse($slot->end_time)->format('H:i');
                $slot->formatted_time = $slot->formatted_time;
                return $slot;
            })->values();

            return ['status' => 200, 'data' => $availableSlots];
        } catch (\Exception $e) {
            return ['status' => 500, 'error' => $e->getMessage()];
        }
    }

    /**
     * Get user bookings - FIXED
     */
    public static function getUserBookings($request)
    {
        try {
            $userId = $request->user_id ?? auth()->id();
            $query = ConsultancyBooking::with(['slot'])
                ->where('user_id', $userId);

            // Apply filters
            if (isset($request->status) && !empty($request->status)) {
                $query->where('status', $request->status);
            }

            $bookings = $query->orderBy('created_at', 'desc')->get();

            return ['status' => 200, 'data' => $bookings];
        } catch (\Exception $e) {
            return ['status' => 500, 'error' => $e->getMessage()];
        }
    }

    /**
     * Initialize user consultancy - FIXED
     */
    public static function initializeUserConsultancy($userId, $packageId)
    {
        try {
            $package = Pricing::findOrFail($packageId);
            
            // Calculate total consultancy minutes from package
            $onlineMinutes = ($package->online_consultancy_hours ?? 0) * 60;
            $inPersonMinutes = 0;
            
            if (isset($package->in_person_services) && is_array($package->in_person_services)) {
                if ($package->in_person_services['status'] ?? false) {
                    $inPersonMinutes = ($package->in_person_services['consultancy_hours'] ?? 0) * 60;
                }
            }
            
            $totalMinutes = $onlineMinutes + $inPersonMinutes;
            $totalHours = $totalMinutes / 60;

            // Create or update consultancy usage
            ConsultancyUsage::updateOrCreate(
                ['user_id' => $userId],
                [
                    'package_id' => $packageId,
                    'total_hours_allocated' => $totalHours,
                    'total_minutes_allocated' => $totalMinutes,
                    'used_minutes' => 0,
                    'remaining_minutes' => $totalMinutes,
                    'reset_date' => now()->addYear(),
                ]
            );

            return ['status' => 200, 'message' => 'Consultancy usage initialized successfully'];
        } catch (\Exception $e) {
            return ['status' => 500, 'error' => $e->getMessage()];
        }
    }
}