<?php

namespace App\Interfaces\Admin\AwarenessManagement;

use Illuminate\Http\Request;

interface AwarenessTopicRepositoryInterface
{
    public function list(Request $request, string $awarenessId): array;
    public function create(Request $request, string $awarenessId): array;
    public function update(Request $request, string $awarenessId, string $topicId): array;
    public function delete(Request $request, string $awarenessId, string $topicId): array;
}