<?php

namespace App\Services\Admin\AwarenessManagement;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class AwarenessValidator
{
    protected $rules = [
        'title' => 'required|string',
        'slug' => 'nullable|string',
        'description' => 'nullable|string',
        'banner' => 'nullable|string',
        'selected_package' => 'required|array',
        'issue_certificate' => 'required|boolean',
    ];

    protected $updateRules = [
        'title' => 'sometimes|required|string',
        'slug' => 'sometimes|nullable|string',
        'description' => 'sometimes|nullable|string',
        'banner' => 'sometimes|nullable|string',
        'selected_package' => 'sometimes|required|array',
        'issue_certificate' => 'sometimes|required|boolean',
    ];

    public function validate(Request $request, bool $isCreate = true): array
    {
        $rules = $isCreate ? $this->rules : $this->updateRules;
        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return $request->only(array_keys($rules));
    }
}