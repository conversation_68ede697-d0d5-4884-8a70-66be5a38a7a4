<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class UserAuthCheck
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Check if the user is not authenticated
        if (!Auth::check()) {
            Log::info('Not authenticated, proceeding');
            return $next($request);
        } else {
            // If the user is authenticated, redirect them to the home page
            Log::info('User already logged in', ['user' => Auth::user()->id]);
            return response()->json(['status' => 1000, 'msg' => 'You are already logged in!']);
        }
    }
}
