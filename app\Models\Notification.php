<?php

namespace App\Models;

use <PERSON><PERSON><PERSON>\Mongodb\Eloquent\Model as Eloquent;

class Notification extends Eloquent
{
    /**
     * The connection name for the model.
     *
     * @var string
     */
    protected $connection = 'mongodb';

    /**
     * The collection associated with the model.
     *
     * @var string
     */
    protected $collection = 'notifications';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'title',
        'message',
        'type',
        'action_url',
        'data',
        'read_at',
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'updated_at'
    ];

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'data' => 'array',
        // Remove the datetime cast for read_at since MongoDB stores it differently
        // 'read_at' => 'datetime'
    ];

    /**
     * Relationship with User
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', '_id');
    }

    /**
     * Scope for unread notifications
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeUnread($query)
    {
        return $query->whereNull('read_at');
    }

    /**
     * Scope for read notifications
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRead($query)
    {
        return $query->whereNotNull('read_at');
    }

    /**
     * Check if notification is read
     *
     * @return bool
     */
    public function getIsReadAttribute()
    {
        return !is_null($this->read_at);
    }

    /**
     * Get formatted created at date
     *
     * @return string
     */
    public function getFormattedCreatedAtAttribute()
    {
        return $this->created_at->diffForHumans();
    }

    /**
     * Custom accessor for read_at to handle MongoDB date formats
     *
     * @param mixed $value
     * @return \Carbon\Carbon|null
     */
    public function getReadAtAttribute($value)
    {
        if (is_null($value)) {
            return null;
        }

        // If it's already a Carbon instance, return it
        if ($value instanceof \Carbon\Carbon) {
            return $value;
        }

        // If it's an array (MongoDB UTCDateTime format), handle it
        if (is_array($value) && isset($value['$date'])) {
            return \Carbon\Carbon::parse($value['$date']);
        }

        // If it's a string, parse it
        if (is_string($value)) {
            return \Carbon\Carbon::parse($value);
        }

        // If it's a DateTime object, convert to Carbon
        if ($value instanceof \DateTime) {
            return \Carbon\Carbon::instance($value);
        }

        return null;
    }

    /**
     * Custom mutator for read_at to ensure proper storage
     *
     * @param mixed $value
     * @return void
     */
    public function setReadAtAttribute($value)
    {
        if (is_null($value)) {
            $this->attributes['read_at'] = null;
            return;
        }

        if ($value instanceof \Carbon\Carbon) {
            $this->attributes['read_at'] = $value->toDateTime();
        } elseif ($value instanceof \DateTime) {
            $this->attributes['read_at'] = $value;
        } elseif (is_string($value)) {
            $this->attributes['read_at'] = \Carbon\Carbon::parse($value)->toDateTime();
        } else {
            $this->attributes['read_at'] = \Carbon\Carbon::now()->toDateTime();
        }
    }
}