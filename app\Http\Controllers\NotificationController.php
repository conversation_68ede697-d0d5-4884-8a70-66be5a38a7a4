<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Notification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class NotificationController extends Controller
{
    /**
     * Get user notifications
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $user = Auth::user();
            $limit = $request->input('limit', 20);
            $page = $request->input('page', 1);
            $offset = ($page - 1) * $limit;

            $notifications = Notification::where('user_id', $user->_id)
                ->orderBy('created_at', 'desc')
                ->skip($offset)
                ->limit($limit)
                ->get();

            $unreadCount = Notification::where('user_id', $user->_id)
                ->whereNull('read_at')
                ->count();

            $totalCount = Notification::where('user_id', $user->_id)->count();

            return response()->json([
                'status' => 200,
                'data' => [
                    'notifications' => $notifications,
                    'unread_count' => $unreadCount,
                    'total_count' => $totalCount,
                    'current_page' => $page,
                    'per_page' => $limit
                ],
                'msg' => 'Notifications retrieved successfully'
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 500,
                'error' => 'Failed to get notifications: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get unread notification count
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function count()
    {
        try {
            $user = Auth::user();
            
            $unreadCount = Notification::where('user_id', $user->_id)
                ->whereNull('read_at')
                ->count();

            return response()->json([
                'status' => 200,
                'data' => ['unread_count' => $unreadCount],
                'msg' => 'Notification count retrieved successfully'
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 500,
                'error' => 'Failed to get notification count: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Mark notification as read
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function markAsRead(Request $request)
    {
        try {
            $user = Auth::user();
            $input = $request->input();

            $validator = Validator::make($input, [
                'notification_id' => 'required|string'
            ]);

            if ($validator->fails()) {
                return response()->json(['status' => 422, 'error' => $validator->errors()], 422);
            }

            $notification = Notification::where('_id', $input['notification_id'])
                ->where('user_id', $user->_id)
                ->first();

            if (!$notification) {
                return response()->json([
                    'status' => 404,
                    'error' => 'Notification not found'
                ], 404);
            }

            $notification->read_at = now();
            $notification->save();

            return response()->json([
                'status' => 200,
                'msg' => 'Notification marked as read'
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 500,
                'error' => 'Failed to mark notification as read: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Mark all notifications as read
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function markAllAsRead()
    {
        try {
            $user = Auth::user();

            Notification::where('user_id', $user->_id)
                ->whereNull('read_at')
                ->update(['read_at' => now()]);

            return response()->json([
                'status' => 200,
                'msg' => 'All notifications marked as read'
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 500,
                'error' => 'Failed to mark all notifications as read: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a notification
     *
     * @param string $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete($id)
    {
        try {
            $user = Auth::user();

            $notification = Notification::where('_id', $id)
                ->where('user_id', $user->_id)
                ->first();

            if (!$notification) {
                return response()->json([
                    'status' => 404,
                    'error' => 'Notification not found'
                ], 404);
            }

            $notification->delete();

            return response()->json([
                'status' => 200,
                'msg' => 'Notification deleted successfully'
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 500,
                'error' => 'Failed to delete notification: ' . $e->getMessage()
            ], 500);
        }
    }
}