<?php

namespace App\Http\Controllers\Admin\AwarenessManagement;

use App\Http\Controllers\Controller;
use App\Interfaces\Admin\AwarenessManagement\AwarenessTopicLessonQuestionRepositoryInterface;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class AwarenessTopicLessonQuestionController extends Controller
{
    private $awarenessTopicLessonQuestionRepository;

    public function __construct(AwarenessTopicLessonQuestionRepositoryInterface $awarenessTopicLessonQuestionRepository)
    {
        $this->awarenessTopicLessonQuestionRepository = $awarenessTopicLessonQuestionRepository;
    }

    public function createOrUpdate(Request $request, string $awarenessId, string $topicId, string $lessonId): JsonResponse
    {
        $response = $this->awarenessTopicLessonQuestionRepository->createOrUpdate($request, $awarenessId, $topicId, $lessonId);
        return response()->json($response, $response['status']);
    }

    public function delete(Request $request, string $awarenessId, string $topicId, string $lessonId, string $questionId): JsonResponse
    {
        $response = $this->awarenessTopicLessonQuestionRepository->delete($request, $awarenessId, $topicId, $lessonId, $questionId);
        return response()->json($response, $response['status']);
    }
}
