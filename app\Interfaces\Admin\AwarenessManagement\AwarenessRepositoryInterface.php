<?php

namespace App\Interfaces\Admin\AwarenessManagement;

use Illuminate\Http\Request;

interface AwarenessRepositoryInterface
{
    public function list(Request $request): array;
    public function create(Request $request): array;
    public function single(Request $request, string $id): array;
    public function update(Request $request, string $id): array;
    public function delete(Request $request, string $id): array;
}