<?php
// FIXED app/Http/Controllers/Admin/ConsultancyController.php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Repositories\ConsultancyRepository;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ConsultancyController extends Controller
{
    /**
     * Get all consultancy slots - FIXED TO USE GET REQUEST
     */
    public function getSlots(Request $request): JsonResponse
    {
        try {
            // Extract query parameters for filtering
            $filters = $request->only(['type', 'date', 'country', 'is_active', 'per_page']);
            $result = ConsultancyRepository::getSlots((object)$filters);
            
            // EMERGENCY: Return raw result without transformation to avoid the array issue
            return response()->json($result, $result['status']);
            
        } catch (\Exception $e) {
            \Log::error('Error in getSlots: ' . $e->getMessage());
            return response()->json([
                'status' => 500,
                'error' => 'Failed to fetch slots: ' . $e->getMessage()
            ], 500);
        }
    }
/**
 * Create a new consultancy slot
 */
public function createSlot(Request $request): JsonResponse
{
    try {
        $validated = $request->validate([
            'date' => 'required|date',
            'start_time' => 'required',
            'end_time' => 'required', 
            'type' => 'required|in:online,in_person',
            'max_capacity' => 'required|integer|min:1|max:50',
            'is_active' => 'sometimes|boolean',
            'country' => 'nullable|string|max:100',
            'city' => 'nullable|string|max:100',
            'location' => 'nullable|string|max:255',
            'address' => 'nullable|string|max:500',
            'notes' => 'nullable|string|max:500',
            'meeting_link_template' => 'nullable|url',
        ]);

        // Set default for is_active if not provided
        if (!isset($validated['is_active'])) {
            $validated['is_active'] = true;
        }

        $result = ConsultancyRepository::manageSlot($request);
        return response()->json($result, $result['status']);
        
    } catch (\Illuminate\Validation\ValidationException $e) {
        \Log::error('Validation failed for createSlot:', $e->errors());
        return response()->json([
            'status' => 422,
            'error' => 'Validation failed',
            'errors' => $e->errors(),
            'data' => $request->all() // Debug: show what was sent
        ], 422);
    } catch (\Exception $e) {
        \Log::error('Error in createSlot: ' . $e->getMessage());
        return response()->json([
            'status' => 500,
            'error' => $e->getMessage()
        ], 500);
    }
}

/**
 * Update an existing consultancy slot
 */
public function updateSlot(Request $request): JsonResponse
{
    try {
        $validated = $request->validate([
            'id' => 'required|string',
            'date' => 'required|date',
            'start_time' => 'required',
            'end_time' => 'required',
            'type' => 'required|in:online,in_person',
            'max_capacity' => 'required|integer|min:1|max:50',
            'is_active' => 'sometimes|boolean',
            'country' => 'nullable|string|max:100',
            'city' => 'nullable|string|max:100',
            'location' => 'nullable|string|max:255',
            'address' => 'nullable|string|max:500',
            'notes' => 'nullable|string|max:500',
            'meeting_link_template' => 'nullable|url',
        ]);

        // Set default for is_active if not provided
        if (!isset($validated['is_active'])) {
            $validated['is_active'] = true;
        }

        $result = ConsultancyRepository::manageSlot($request);
        return response()->json($result, $result['status']);
        
    } catch (\Illuminate\Validation\ValidationException $e) {
        \Log::error('Validation failed for updateSlot:', $e->errors());
        return response()->json([
            'status' => 422,
            'error' => 'Validation failed',
            'errors' => $e->errors(),
            'data' => $request->all() // Debug: show what was sent
        ], 422);
    } catch (\Exception $e) {
        \Log::error('Error in updateSlot: ' . $e->getMessage());
        return response()->json([
            'status' => 500,
            'error' => $e->getMessage()
        ], 500);
    }
}
    /**
     * Delete a consultancy slot
     */
    public function deleteSlot($id): JsonResponse
    {
        $result = ConsultancyRepository::deleteSlot($id);
        return response()->json($result, $result['status']);
    }

    /**
     * Get all bookings - FIXED TO USE GET REQUEST
     */
    public function getBookings(Request $request): JsonResponse
    {
        // Extract query parameters for filtering
        $filters = $request->only(['status', 'date_from', 'date_to', 'user_search', 'per_page']);
        $result = ConsultancyRepository::getBookings((object)$filters);
        return response()->json($result, $result['status']);
    }

    /**
     * Get single booking
     */
    public function getBooking($id): JsonResponse
    {
        $result = ConsultancyRepository::getBooking($id);
        return response()->json($result, $result['status']);
    }

    /**
     * Get single slot
     */
    public function getSlot($id): JsonResponse
    {
        $result = ConsultancyRepository::getSingleSlot($id);
        return response()->json($result, $result['status']);
    }

    /**
     * Update an existing booking
     */
    public function updateBooking(Request $request): JsonResponse
    {
        $request->validate([
            'booking_id' => 'required|string',
            'status' => 'nullable|in:pending,confirmed,cancelled,completed',
            'meeting_link' => 'nullable|url',
            'notes' => 'nullable|string|max:500',
            'cancellation_reason' => 'nullable|string|max:300',
        ]);

        $result = ConsultancyRepository::updateBooking($request);
        return response()->json($result, $result['status']);
    }

    /**
     * Cancel a booking
     */
    public function cancelBooking(Request $request): JsonResponse
    {
        $request->validate([
            'booking_id' => 'required|string',
            'cancellation_reason' => 'nullable|string|max:300',
        ]);

        $result = ConsultancyRepository::cancelBooking($request);
        return response()->json($result, $result['status']);
    }

    /**
     * Confirm a booking
     */
    public function confirmBooking(Request $request): JsonResponse
    {
        $request->validate(['booking_id' => 'required|string']);
        
        try {
            $booking = \App\Models\ConsultancyBooking::findOrFail($request->booking_id);
            $booking->status = 'confirmed';
            $booking->save();
            
            return response()->json([
                'status' => 200,
                'message' => 'Booking confirmed successfully',
                'data' => $booking->load(['user', 'slot'])
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 500,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update meeting link for a booking
     */
    public function updateMeetingLink(Request $request): JsonResponse
    {
        $request->validate([
            'booking_id' => 'required|string',
            'meeting_link' => 'required|url',
        ]);

        try {
            $booking = \App\Models\ConsultancyBooking::findOrFail($request->booking_id);
            $booking->meeting_link = $request->meeting_link;
            $booking->save();

            return response()->json([
                'status' => 200,
                'message' => 'Meeting link updated successfully',
                'data' => $booking
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 500,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send meeting link to user
     */
    public function sendMeetingLink(Request $request): JsonResponse
    {
        $request->validate(['booking_id' => 'required|string']);

        try {
            $booking = \App\Models\ConsultancyBooking::with(['user', 'slot'])
                ->findOrFail($request->booking_id);

            if (!$booking->meeting_link) {
                return response()->json([
                    'status' => 400,
                    'error' => 'No meeting link available for this booking'
                ], 400);
            }

            // Here you would implement email sending logic
            // For now, we'll just mark it as sent
            $booking->reminder_sent = true;
            $booking->save();

            return response()->json([
                'status' => 200,
                'message' => 'Meeting link sent successfully',
                'data' => $booking
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 500,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get consultancy statistics - USES GET REQUEST CORRECTLY
     */
    public function getStats(): JsonResponse
    {
        $result = ConsultancyRepository::getConsultancyStats();
        return response()->json($result, $result['status']);
    }

    /**
     * Get booking reports
     */
    public function getBookingReport(Request $request): JsonResponse
    {
        try {
            $query = \App\Models\ConsultancyBooking::with(['user', 'slot']);
            
            // Apply date filters
            if ($request->date_from) {
                $query->whereHas('slot', function($q) use ($request) {
                    $q->where('date', '>=', $request->date_from);
                });
            }
            
            if ($request->date_to) {
                $query->whereHas('slot', function($q) use ($request) {
                    $q->where('date', '<=', $request->date_to);
                });
            }
            
            // Apply status filter
            if ($request->status) {
                $query->where('status', $request->status);
            }
            
            $bookings = $query->orderBy('created_at', 'desc')->get();
            
            // Generate summary statistics
            $summary = [
                'total_bookings' => $bookings->count(),
                'confirmed_bookings' => $bookings->where('status', 'confirmed')->count(),
                'pending_bookings' => $bookings->where('status', 'pending')->count(),
                'cancelled_bookings' => $bookings->where('status', 'cancelled')->count(),
                'completed_bookings' => $bookings->where('status', 'completed')->count(),
            ];
            
            return response()->json([
                'status' => 200,
                'data' => [
                    'bookings' => $bookings,
                    'summary' => $summary
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 500,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get usage reports
     */
    public function getUsageReport(Request $request): JsonResponse
    {
        try {
            $query = \App\Models\ConsultancyUsage::with(['user', 'package']);
            
            // Apply filters
            if ($request->package_id) {
                $query->where('package_id', $request->package_id);
            }
            
            $usageData = $query->get();
            
            // Calculate summary statistics
            $totalAllocated = $usageData->sum('total_minutes_allocated');
            $totalUsed = $usageData->sum('used_minutes');
            $totalRemaining = $usageData->sum('remaining_minutes');
            
            $summary = [
                'total_users' => $usageData->count(),
                'total_hours_allocated' => round($totalAllocated / 60, 2),
                'total_hours_used' => round($totalUsed / 60, 2),
                'total_hours_remaining' => round($totalRemaining / 60, 2),
                'usage_percentage' => $totalAllocated > 0 ? round(($totalUsed / $totalAllocated) * 100, 2) : 0,
            ];
            
            return response()->json([
                'status' => 200,
                'data' => [
                    'usage_data' => $usageData,
                    'summary' => $summary
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 500,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get user usage
     */
    public function getUserUsage(Request $request): JsonResponse
    {
        $request->validate(['user_id' => 'required|string']);
        
        try {
            $usage = \App\Models\ConsultancyUsage::with(['user', 'package'])
                ->where('user_id', $request->user_id)
                ->first();
                
            if (!$usage) {
                return response()->json([
                    'status' => 404,
                    'error' => 'Usage record not found for this user'
                ], 404);
            }
            
            // Get user's booking history
            $bookings = \App\Models\ConsultancyBooking::with('slot')
                ->where('user_id', $request->user_id)
                ->orderBy('created_at', 'desc')
                ->get();
            
            return response()->json([
                'status' => 200,
                'data' => [
                    'usage' => $usage,
                    'bookings' => $bookings
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 500,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update user usage
     */
    public function updateUserUsage(Request $request): JsonResponse
    {
        $request->validate([
            'user_id' => 'required|string',
            'remaining_minutes' => 'required|integer|min:0',
            'reason' => 'nullable|string|max:255',
        ]);

        try {
            $usage = \App\Models\ConsultancyUsage::where('user_id', $request->user_id)->first();
            
            if (!$usage) {
                return response()->json([
                    'status' => 404,
                    'error' => 'Usage record not found for this user'
                ], 404);
            }

            $oldRemaining = $usage->remaining_minutes;
            $usage->remaining_minutes = $request->remaining_minutes;
            $usage->used_minutes = $usage->total_minutes_allocated - $request->remaining_minutes;
            $usage->save();

            // Log the change if reason provided
            if ($request->reason) {
                \Log::info("Admin updated user consultancy usage", [
                    'user_id' => $request->user_id,
                    'old_remaining' => $oldRemaining,
                    'new_remaining' => $request->remaining_minutes,
                    'reason' => $request->reason,
                    'admin_id' => auth()->id()
                ]);
            }

            return response()->json([
                'status' => 200,
                'message' => 'User usage updated successfully',
                'data' => $usage
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 500,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Initialize consultancy for a user
     */
    public function initializeUserConsultancy(Request $request): JsonResponse
    {
        $request->validate([
            'user_id' => 'required|string',
            'package_id' => 'required|string',
        ]);

        $result = ConsultancyRepository::initializeUserConsultancy(
            $request->user_id, 
            $request->package_id
        );
        
        return response()->json($result, $result['status']);
    }

    /**
     * Get countries list
     */
    public function getCountries(): JsonResponse
    {
        $countries = [
            'United States', 'United Kingdom', 'Canada', 'Australia', 'Germany', 
            'France', 'Netherlands', 'Sweden', 'Norway', 'Denmark', 'Switzerland',
            'Austria', 'Belgium', 'Italy', 'Spain', 'Portugal', 'Ireland', 'Finland',
            'Iceland', 'Luxembourg', 'New Zealand', 'Singapore', 'Japan', 'South Korea'
        ];

        return response()->json([
            'status' => 200,
            'data' => $countries
        ]);
    }

    /**
     * Get cities for a country
     */
    public function getCities($country): JsonResponse
    {
        // Sample cities - in a real app, you'd query a database
        $cities = [
            'United States' => ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix'],
            'United Kingdom' => ['London', 'Manchester', 'Birmingham', 'Liverpool', 'Bristol'],
            'Canada' => ['Toronto', 'Vancouver', 'Montreal', 'Calgary', 'Ottawa'],
            'Australia' => ['Sydney', 'Melbourne', 'Brisbane', 'Perth', 'Adelaide'],
            'Germany' => ['Berlin', 'Munich', 'Hamburg', 'Cologne', 'Frankfurt'],
            'France' => ['Paris', 'Lyon', 'Marseille', 'Toulouse', 'Nice'],
        ];

        $countryCities = $cities[$country] ?? [];

        return response()->json([
            'status' => 200,
            'data' => $countryCities
        ]);
    }
}