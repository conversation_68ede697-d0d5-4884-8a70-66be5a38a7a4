<?php

namespace App\Services;

use Stripe\Stripe;
use Stripe\Product;
use Stripe\Price;
use Illuminate\Support\Facades\Log;

class StripeService
{
    public function __construct()
    {
        Stripe::setApiKey(env('STRIPE_SECRET_KEY'));
    }

    public function createStripeProductAndPrice(array $package): array
    {
        $product = Product::create([ 'name' => $package['package_name'], 'description' => 'Auto-created from admin panel']);
        $price = Price::create([ 'product' => $product->id, 'unit_amount' => (int)($package['package_price'] * 100), 'currency' => strtolower($package['currency'])]);
        return [ 'stripe_product_id' => $product->id, 'stripe_price_id' => $price->id];
    }

    public function archiveProductIfExists(?string $productId): void
    {
        if (empty($productId)) return;
        try {
            Product::update($productId, ['active' => false]);
        } catch (\Exception $e) {
            Log::warning("Stripe Product deletion failed: " . $e->getMessage());
        }
    }

    public function updateProductAndPrice(array $validated_price_package, string $productId): array
    {
        $stripeData = [];

        try {
            // Create or update product
            if ($productId) {
                $product = Product::update($productId, [ 'name' => $validated_price_package['package_name'], 'description' => 'Updated from admin panel']);
            } else {
                $product = Product::create([ 'name' => $validated_price_package['package_name'], 'description' => 'Auto-created from admin panel']);
            }
            $stripeData['stripe_product_id'] = $product->id;

            $prices = Price::all(['product' => $product->id])['data'];
            foreach ($prices as $price) {
                if ($price->active) {
                    Price::update($price->id, ['active' => false]);
                    Log::info("Archived active price {$price->id} for product {$product->id}");
                }
            }

            $price = Price::create([ 'product' => $product->id, 'unit_amount' => (int)($validated_price_package['package_price'] * 100),'currency' => strtolower($validated_price_package['currency'])]);

            $stripeData['stripe_price_id'] = $price->id;
        } catch (\Exception $e) {
            Log::error('Stripe update/create failed: ' . $e->getMessage());
        }
        return $stripeData;
    }
}