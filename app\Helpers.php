<?php

namespace App;

use Google\Cloud\RecaptchaEnterprise\V1\RecaptchaEnterpriseServiceClient;
use Google\Cloud\RecaptchaEnterprise\V1\Event;
use Google\Cloud\RecaptchaEnterprise\V1\Assessment;
use Google\Cloud\RecaptchaEnterprise\V1\TokenProperties\InvalidReason;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\File;

class Helpers
{
    /**
     * Format a number with two decimal places.
     *
     * @param float $number
     * @return string
     */
    public static function num2format($number)
    {
        return number_format($number, 2, '.', '');
    }

    /**
     * Ask a question to the ChatGPT model and get the response.
     *
     * @param string $question
     * @return string|null
     */
    public static function askChatGPT($question)
    {
        $curl = curl_init();
        $openapiKey = getenv('OPENAI_API_KEY');
        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://api.openai.com/v1/chat/completions',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => '{
                "model": "gpt-3.5-turbo",
                "messages": [
                    {"role": "system", "content": "You are a Multi-Scale Operation-assurance Evaluation Tool for AI Systems"},
                    {"role": "user", "content": "' . $question . '"}]}',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                'Authorization: Bearer ' . $openapiKey
            ),
        ));
        $response = curl_exec($curl);
        curl_close($curl);

        // Parse the response and extract the content from the first choice
        $response = json_decode($response, true);
        $content = null;
        if (isset($response['choices']) && isset($response['choices'][0])) {
            $content = nl2br($response['choices'][0]['message']['content']);
        }

        return $content;
    }

    /**
     * Sends a prompt to the Google Gemini API and returns the response.
     *
     * @param string $question The user's question or prompt.
     * @return string|null The generated text response, or null on failure.
    */
    public static function askGemini($question)
    {
        $apiKey = env('GEMINI_API_KEY');
        $model = env('GEMINI_MODEL');
        if (!$apiKey) {
            Log::error("GEMINI_API_KEY not set in environment.");
            return "Error: Gemini API key not configured.";
        }
        $url = "https://generativelanguage.googleapis.com/v1beta/models/{$model}:generateContent?key=".$apiKey;

        $contents = [];
        $systemInstruction = File::get(resource_path('prompts/system-prompt.txt'));

        if ($systemInstruction !== null) {
             $contents[] = [
                 'role' => 'user',
                 'parts' => [['text' => $systemInstruction]]
             ];
        }
        $contents[] = [
            'role' => 'user',
            'parts' => [['text' => $question]]
        ];

        $body = ['contents' => $contents,];

        try {
            $response = Http::post($url, $body);
            if ($response->successful()) {
                $responseData = $response->json();
                Log::info('Gemini API Response Data:', $responseData);

                // Extract the generated text from the response
                // The structure is typically candidates[0].content.parts[0].text
                if (isset($responseData['candidates'][0]['content']['parts'][0]['text'])) {
                    return nl2br($responseData['candidates'][0]['content']['parts'][0]['text']);
                } else {
                    Log::warning('Gemini API Response missing expected content structure.', ['response' => $responseData]);
                    return "Error: Could not parse Gemini response.";
                }
            } else {
                Log::error('Gemini API Error Response:', [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);

                $error = $response->json();
                $errorMessage = $error['error']['message'] ?? 'Unknown API error';
                return "Error: Gemini API call failed with status " . $response->status() . ". " . $errorMessage;
            }

        } catch (\Exception $e) {
            Log::error('Gemini API Exception:', ['message' => $e->getMessage()]);
            return "Error: An exception occurred during the Gemini API call.";
        }
    }




    public static function verifyRecaptcha($token)
    {
        try {
            $param = array(
                'secret' => env('RECAPTCHA_SECRET_KEY'),
                'response' => $token,
            );
            $curl = curl_init();
            curl_setopt_array($curl, array(
                CURLOPT_URL => 'https://www.google.com/recaptcha/api/siteverify',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => $param,
            ));
            $response = curl_exec($curl);
            curl_close($curl);
            $response = json_decode($response, true);
            return $response;

        } catch (\Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Parse the video ID from a YouTube video link.
     *
     * @param string $link
     * @return string|null
     */
    public static function parseYoutubeVideoId($link)
    {
        $video_id = explode("?v=", $link);
        if (empty($video_id[1])) {
            $video_id = explode("/v/", $link);
        }
        $video_id = explode("&", $video_id[1] ?? '');
        return $video_id[0] ?? null;
    }
}
