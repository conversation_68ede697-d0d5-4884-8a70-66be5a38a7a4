<?php

namespace App\Repositories\Admin\AwarenessManagement;

use Illuminate\Http\Request;
use App\Interfaces\Admin\AwarenessManagement\AwarenessTopicLessonRepositoryInterface;
use App\Services\Admin\AwarenessManagement\LessonValidator;
use App\Services\Admin\AwarenessManagement\ImageHandler;
use App\Repositories\Admin\AwarenessManagement\Traits\AwarenessHierarchyTrait;

class AwarenessTopicLessonRepository implements AwarenessTopicLessonRepositoryInterface
{
    use AwarenessHierarchyTrait;
    protected $lessonValidator;
    protected $imageHandler;

    public function __construct(LessonValidator $lessonValidator, ImageHandler $imageHandler)
    {
        $this->lessonValidator = $lessonValidator;
        $this->imageHandler = $imageHandler;
    }

    public function create(Request $request, string $awarenessId, string $topicId): array
    {
        try {
          
            $topic = $this->getTopic($awarenessId, $topicId);
            $validatedData = $this->lessonValidator->validate($request, true);

            if (!empty($request->description)) {
                $validatedData['description'] = $this->imageHandler->handleBase64Images($request->description);
            }

            $validatedData['order'] = $request->has('order') ? $request->order : ($topic->lessons()->max('order') ?? 0) + 1;

            $lesson = $topic->lessons()->create($validatedData);

            return ['status' => 200, 'data' => ['lesson_id' => (string)$lesson->_id], 'message' => 'Lesson created successfully'];
        } catch (\Exception $e) {
            return ['status' => 500, 'error' => $e->getMessage()];
        }
    }

    public function single(Request $request, string $awarenessId, string $topicId, string $lessonId): array
    {
        try {
            $lesson = $this->getLesson($awarenessId, $topicId, $lessonId)->load('questions');
            return ['status' => 200, 'data' => $lesson->toArray()];
        } catch (\Exception $e) {
            return ['status' => 500, 'message' => $e->getMessage()];
        }
    }

    public function update(Request $request, string $awarenessId, string $topicId, string $lessonId): array
    {
        try {
            $lesson = $this->getLesson($awarenessId, $topicId, $lessonId);
            $validatedData = $this->lessonValidator->validate($request, false);

            if ($request->has('description')) {
                $validatedData['description'] = $this->imageHandler->handleBase64Images($request->description);
            }

            $lesson->update($validatedData);

            return ['status' => 200, 'message' => 'Lesson updated successfully'];
        } catch (\Exception $e) {
            return ['status' => 500, 'error' => $e->getMessage()];
        }
    }

    public function delete(Request $request, string $awarenessId, string $topicId, string $lessonId): array
    {
        try {
            $lesson = $this->getLesson($awarenessId, $topicId, $lessonId);
            $lesson->questions()->delete();
            $lesson->delete();

            return ['status' => 200, 'message' => 'Lesson and its questions deleted successfully'];
        } catch (\Exception $e) {
            return ['status' => 500, 'message' => $e->getMessage()];
        }
    }
}