<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\StripeService;
use App\Services\SubscriptionService;
use App\Interfaces\Admin\AwarenessManagement\AwarenessRepositoryInterface;
use App\Interfaces\Admin\AwarenessManagement\AwarenessTopicRepositoryInterface;
use App\Interfaces\Admin\AwarenessManagement\AwarenessTopicLessonRepositoryInterface;
use App\Interfaces\Admin\AwarenessManagement\AwarenessTopicLessonQuestionRepositoryInterface;
use App\Repositories\Admin\AwarenessManagement\AwarenessRepository;
use App\Repositories\Admin\AwarenessManagement\AwarenessTopicRepository;
use App\Repositories\Admin\AwarenessManagement\AwarenessTopicLessonRepository;
use App\Repositories\Admin\AwarenessManagement\AwarenessTopicLessonQuestionRepository;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register()
    {
        $this->app->singleton(StripeService::class, function ($app) {
            return new StripeService();
        });

        $this->app->singleton(SubscriptionService::class, function ($app) {
            return new SubscriptionService();
        });
        
        $this->app->singleton(\Stripe\StripeClient::class, function () {
            return new \Stripe\StripeClient(env('STRIPE_SECRET_KEY'));
        });

        $this->app->bind(AwarenessRepositoryInterface::class, AwarenessRepository::class);
        $this->app->bind(AwarenessTopicRepositoryInterface::class, AwarenessTopicRepository::class);
        $this->app->bind(AwarenessTopicLessonRepositoryInterface::class, AwarenessTopicLessonRepository::class);
        $this->app->bind(AwarenessTopicLessonQuestionRepositoryInterface::class, AwarenessTopicLessonQuestionRepository::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot()
    {
        //
    }
}