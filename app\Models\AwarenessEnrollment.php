<?php

namespace App\Models;

use <PERSON><PERSON><PERSON>\Mongodb\Eloquent\Model as Eloquent;

class AwarenessEnrollment extends Eloquent
{
    protected $connection = 'mongodb';
    protected $collection = 'awareness_enrollments';
    protected $fillable = [
        'user_id',
        'course_data',
        'topics_data',
        'lessons_data',
    ];
    protected $hidden = [
        'updated_at',
        'created_at'
    ];
    public $timestamps = true;

    protected $casts = [
        'course_data' => 'array',
        'topics_data' => 'array',
        'lessons_data' => 'array',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function awareness()
    {
        return $this->belongsTo(Awareness::class, 'course_data.course_id');
    }
}
