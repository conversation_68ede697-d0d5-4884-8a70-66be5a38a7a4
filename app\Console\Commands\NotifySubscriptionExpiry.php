<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\PackagePurchase;
use App\Services\NotificationService;
use Carbon\Carbon;

class NotifySubscriptionExpiry extends Command
{
    protected $signature = 'notify:subscription-expiry';
    protected $description = 'Send notifications for expiring subscriptions';

    public function handle()
    {
        $this->info('Checking for expiring subscriptions...');
        
        // Find users with subscriptions expiring in 7 days
        $expiringIn7Days = PackagePurchase::where('expires_at', '>=', now())
            ->where('expires_at', '<=', now()->addDays(7))
            ->where('status', 'active')
            ->get();

        foreach ($expiringIn7Days as $purchase) {
            $daysLeft = now()->diffInDays($purchase->expires_at);
            
            NotificationService::subscriptionExpiring(
                $purchase->user_id,
                $daysLeft
            );
            
            $this->info("Notified user {$purchase->user_id} - {$daysLeft} days left");
        }
        
        $this->info('Subscription expiry notifications sent!');
    }
}

